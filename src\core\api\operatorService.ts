import { httpGet } from './httpClient'
import { endpoints } from './endpoints'

export interface OperatorDto {
	name: string
}

/**
 * Fetch operator names from the API
 */
export async function fetchOperatorNames(signal?: AbortSignal): Promise<OperatorDto[]> {
	return httpGet<OperatorDto[]>(endpoints.operatorNames, { 
		signal, 
		headers: { 'cache-control': 'no-cache' } 
	})
}

/**
 * Hook for managing operator names with retry logic
 * Continuously retries fetching operator names every second when the list is empty
 */
export class OperatorNamesManager {
	private operators: OperatorDto[] = []
	private listeners: Set<(operators: OperatorDto[]) => void> = new Set()
	private retryInterval: number | null = null
	private abortController: AbortController | null = null
	private isDestroyed = false

	constructor() {
		this.startFetching()
	}

	/**
	 * Subscribe to operator names updates
	 */
	subscribe(listener: (operators: OperatorDto[]) => void): () => void {
		this.listeners.add(listener)
		// Immediately call with current data
		listener(this.operators)
		
		return () => {
			this.listeners.delete(listener)
		}
	}

	/**
	 * Get current operator names
	 */
	getOperators(): OperatorDto[] {
		return [...this.operators]
	}

	/**
	 * Start fetching operator names
	 */
	private startFetching(): void {
		if (this.isDestroyed) return

		this.fetchOperators()
	}

	/**
	 * Fetch operator names and handle retry logic
	 */
	private async fetchOperators(): Promise<void> {
		if (this.isDestroyed) return

		try {
			// Cancel previous request if still pending
			if (this.abortController) {
				this.abortController.abort()
			}

			this.abortController = new AbortController()
			const operators = await fetchOperatorNames(this.abortController.signal)
			
			if (this.isDestroyed) return

			this.operators = operators || []
			this.notifyListeners()

			// If operators list is empty, retry after 1 second
			if (this.operators.length === 0) {
				this.scheduleRetry()
			} else {
				// If we have operators, clear any existing retry
				this.clearRetry()
			}

		} catch (error) {
			if (this.isDestroyed) return

			// Don't log abort errors
			if (error instanceof Error && error.name !== 'AbortError') {
				console.warn('Failed to fetch operator names:', error.message)
			}

			// Retry after 1 second on error
			this.scheduleRetry()
		}
	}

	/**
	 * Schedule a retry after 1 second
	 */
	private scheduleRetry(): void {
		if (this.isDestroyed) return

		this.clearRetry()
		this.retryInterval = window.setTimeout(() => {
			if (!this.isDestroyed) {
				this.fetchOperators()
			}
		}, 1000)
	}

	/**
	 * Clear retry interval
	 */
	private clearRetry(): void {
		if (this.retryInterval !== null) {
			clearTimeout(this.retryInterval)
			this.retryInterval = null
		}
	}

	/**
	 * Notify all listeners of operator names update
	 */
	private notifyListeners(): void {
		this.listeners.forEach(listener => {
			try {
				listener(this.operators)
			} catch (error) {
				console.error('Error in operator names listener:', error)
			}
		})
	}

	/**
	 * Manually trigger a refresh of operator names
	 */
	refresh(): void {
		if (!this.isDestroyed) {
			this.fetchOperators()
		}
	}

	/**
	 * Destroy the manager and clean up resources
	 */
	destroy(): void {
		this.isDestroyed = true
		this.clearRetry()
		
		if (this.abortController) {
			this.abortController.abort()
			this.abortController = null
		}
		
		this.listeners.clear()
		this.operators = []
	}
}

// Global instance for the application
let globalOperatorManager: OperatorNamesManager | null = null

/**
 * Get the global operator names manager instance
 */
export function getOperatorNamesManager(): OperatorNamesManager {
	if (!globalOperatorManager) {
		globalOperatorManager = new OperatorNamesManager()
	}
	return globalOperatorManager
}

/**
 * Cleanup the global operator names manager
 */
export function cleanupOperatorNamesManager(): void {
	if (globalOperatorManager) {
		globalOperatorManager.destroy()
		globalOperatorManager = null
	}
}
