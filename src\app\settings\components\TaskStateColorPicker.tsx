import { Box, Typography } from '@mui/material'
import { ColorInput } from './ColorInput'
import { useTranslation } from 'react-i18next'
import { type TaskStateColorSettings } from '../../../common/utils/taskStateColors'

interface TaskStateColorPickerProps {
	colors: TaskStateColorSettings
	onChange: (colors: TaskStateColorSettings) => void
}

export default function TaskStateColorPicker({
	colors,
	onChange,
}: TaskStateColorPickerProps) {
	const { t } = useTranslation()

	const handleColorChange = (state: keyof TaskStateColorSettings, color: string) => {
		onChange({
			...colors,
			[state]: color,
		})
	}

	const colorConfigs = [
		{
			key: 'normal' as const,
			label: t('settings.colors.normal', { defaultValue: 'Normal' }),
		},
		{
			key: 'working' as const,
			label: t('settings.colors.working', { defaultValue: 'Working' }),
		},
		{
			key: 'completed' as const,
			label: t('settings.colors.completed', { defaultValue: 'Completed' }),
		},
		{
			key: 'delay' as const,
			label: t('settings.colors.delay', { defaultValue: 'Delay' }),
		},
		{
			key: 'interrupt' as const,
			label: t('settings.colors.interrupt', { defaultValue: 'Interrupt' }),
		},
	]

	return (
		<Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
			<Typography variant="subtitle1" fontWeight={600} fontSize={'1rem'}>
				{t('settings.colors.title', { defaultValue: 'Table Row Colors' })}
			</Typography>

			<Box>
				<Box sx={{ display: 'flex', flexWrap: 'wrap', alignContent: 'stretch', gap: 3 }}>
					{colorConfigs.map((config) => (
						<ColorInput
							key={config.key}
							label={config.label}
							value={colors[config.key]}
							onChange={(color) => handleColorChange(config.key, color)}
						/>
					))}
				</Box>

				<Typography variant="body2" color="text.secondary">
					{t('settings.colors.noteText', {
						defaultValue:
							'These colors only apply to table rows. Header colors are determined by alert status and remain unchanged.',
					})}
				</Typography>
			</Box>
		</Box>
	)
}
