import {
	Box,
	Paper,
	Typography,
	Button,
	CircularProgress,
	Chip,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
} from '@mui/material'
import { useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { importPlannedWork } from '../services/plannedWorkImport'
import type { PlannedWorkImportResponse } from '../../../models/PlannedWork'

interface ImportPlannedWorkProps {
	onImportSuccess: (success: string) => void
	onImportError: (error: string) => void
}

export const ImportPlannedWork: React.FC<ImportPlannedWorkProps> = ({
	onImportSuccess,
	onImportError,
}) => {
	const { t } = useTranslation()
	const inputRef = useRef<HTMLInputElement | null>(null)
	const [isImporting, setIsImporting] = useState(false)
	const [importResult, setImportResult] = useState<PlannedWorkImportResponse | null>(null)

	const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
		const files = event.target.files
		if (!files || files.length === 0) return

		try {
			const file = files[0]
			// setSelectedFile(file)
			setIsImporting(true)
			// Clear previous results
			setImportResult(null)

			// TODO: Call import service
			const response = await importPlannedWork(file)

			// Store the import result for display
			setImportResult(response)

			// Determine success based on whether any records were successfully processed
			const totalSuccessful = response.successfulInserts + response.successfulUpdates
			const isSuccess = totalSuccessful > 0

			if (isSuccess) {
				let successMessage = t('settings.importSuccess', {
					defaultValue:
						'Successfully imported {{count}} tasks ({{inserted}} new, {{updated}} updated)',
					count: totalSuccessful,
					inserted: response.successfulInserts,
					updated: response.successfulUpdates,
				})

				// Add warning information if there were failed records
				if (response.failedRecords > 0) {
					successMessage += `. ${response.failedRecords} records failed.`
				}

				onImportSuccess(successMessage)

				// Show warnings if any
				if (response.warnings && response.warnings.length > 0) {
					console.warn('Import warnings:', response.warnings)
				}

				// Show errors if any (for partial success cases)
				if (response.errors && response.errors.length > 0) {
					console.error('Import errors:', response.errors)
				}
			} else {
				let errorMessage =
					response.message ||
					t('settings.importError', { defaultValue: 'Failed to import Excel file' })

				// Add specific error details if available
				if (response.errors && response.errors.length > 0) {
					console.error('Import errors:', response.errors)
					// Show first error in notification for brevity
					errorMessage += `: ${response.errors[0]}`
				}

				onImportError(errorMessage)
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Unknown error'
			onImportError(errorMessage)
		} finally {
			setIsImporting(false)
			if (inputRef.current) {
				inputRef.current.value = ''
			}
		}
	}

	return (
		<Box sx={{ p: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
			<Typography variant="h6">
				{t('settings.importPlannedTasks', { defaultValue: 'Import Planned Tasks' })}
			</Typography>

			<Paper sx={{ height: 'calc(50dvh - 100px)', overflow: 'auto', p: 1, border: '1px solid #ccc', borderRadius: 1, display: 'flex', flexDirection: 'column', alignItems: 'flex-start', gap: 1 }}>
				<input
					ref={inputRef}
					type="file"
					accept=".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
					className="hidden"
					onChange={handleImport}
				/>
				<Button
					variant="contained"
					component="span"
					onClick={() => inputRef.current?.click()}
					disabled={isImporting}
					sx={{ mr: 2 }}
				>
					{isImporting ? (
						<>
							<CircularProgress size={16} />
							&nbsp;{t('settings.importing', { defaultValue: 'Importing...' })}
						</>
					) : (
						<Typography variant="button" sx={{ textTransform: 'none', fontWeight: 600, fontSize: '1rem' }}>
							{t('settings.import', { defaultValue: 'Import' })}
						</Typography>
					)}
				</Button>

				{/* Import Result Statistics */}
				<Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
					<Chip
						label={`${t('settings.totalRecords', { defaultValue: 'Total' })}: ${importResult?.totalRecords ?? 0}`}
						variant="outlined"
						size="small"
						sx={{ backgroundColor: '#f5f5f5' }}
					/>
					<Chip
						label={`${t('settings.successfulInserts', { defaultValue: 'New' })}: ${importResult?.successfulInserts ?? 0}`}
						variant="outlined"
						size="small"
						sx={{ backgroundColor: '#e8f5e8', color: '#2e7d32' }}
					/>
					<Chip
						label={`${t('settings.successfulUpdates', { defaultValue: 'Updated' })}: ${importResult?.successfulUpdates ?? 0}`}
						variant="outlined"
						size="small"
						sx={{ backgroundColor: '#e3f2fd', color: '#1976d2' }}
					/>
					<Chip
						label={`${t('settings.failedRecords', { defaultValue: 'Failed' })}: ${importResult?.failedRecords ?? 0}`}
						variant="outlined"
						size="small"
						sx={{
							backgroundColor: (importResult?.failedRecords ?? 0) > 0 ? '#ffebee' : '#f5f5f5',
							color: (importResult?.failedRecords ?? 0) > 0 ? '#d32f2f' : 'text.secondary'
						}}
					/>
				</Box>

				{/* Errors and Warnings Table */}
				<TableContainer>
					<Table stickyHeader size="small" sx={{ border: '1px solid #e0e0e0' }}>
						<TableHead>
							<TableRow sx={{ backgroundColor: '#f5f5f5' }}>
								<TableCell sx={{ position: 'sticky', top: 0, fontWeight: 600, fontSize: '0.75rem' }}>
									{t('settings.type', { defaultValue: 'Type' })}
								</TableCell>
								<TableCell sx={{ fontWeight: 600, fontSize: '0.75rem' }}>
									{t('settings.message', { defaultValue: 'Message' })}
								</TableCell>
							</TableRow>
						</TableHead>
						<TableBody>
							{importResult && ((importResult.errors && importResult.errors.length > 0) ||
								(importResult.warnings && importResult.warnings.length > 0)) && (
									<>
										{importResult.errors?.map((error, index) => (
											<TableRow key={`error-${index}`}>
												<TableCell sx={{ fontSize: '0.75rem' }}>
													<Chip
														label={t('settings.error', { defaultValue: 'Error' })}
														size="small"
														color="error"
														variant="outlined"
													/>
												</TableCell>
												<TableCell sx={{ fontSize: '0.75rem' }}>{error}</TableCell>
											</TableRow>
										))}
										{importResult.warnings?.map((warning, index) => (
											<TableRow key={`warning-${index}`}>
												<TableCell sx={{ fontSize: '0.75rem' }}>
													<Chip
														label={t('settings.warning', { defaultValue: 'Warning' })}
														size="small"
														color="warning"
														variant="outlined"
													/>
												</TableCell>
												<TableCell sx={{ fontSize: '0.75rem' }}>{warning}</TableCell>
											</TableRow>
										))}
									</>
								)}
						</TableBody>
					</Table>
				</TableContainer>
			</Paper>
		</Box>
	)
}