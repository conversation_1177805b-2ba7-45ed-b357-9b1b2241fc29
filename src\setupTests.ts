/**
 * Jest setup file for global test configuration
 */

import '@testing-library/jest-dom'

// Mock window.matchMedia for orientation tests
Object.defineProperty(window, 'matchMedia', {
	writable: true,
	value: jest.fn().mockImplementation((query) => ({
		matches: false,
		media: query,
		onchange: null,
		addListener: jest.fn(), // Deprecated
		removeListener: jest.fn(), // Deprecated
		addEventListener: jest.fn(),
		removeEventListener: jest.fn(),
		dispatchEvent: jest.fn(),
	})),
})

// Mock ResizeObserver
;(globalThis as unknown as { ResizeObserver: unknown }).ResizeObserver = jest
	.fn()
	.mockImplementation(() => ({
		observe: jest.fn(),
		unobserve: jest.fn(),
		disconnect: jest.fn(),
	}))

// Mock IntersectionObserver
;(globalThis as unknown as { IntersectionObserver: unknown }).IntersectionObserver = jest
	.fn()
	.mockImplementation(() => ({
		observe: jest.fn(),
		unobserve: jest.fn(),
		disconnect: jest.fn(),
	}))

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
	value: jest.fn(),
	writable: true,
})

// Mock fetch for tests
;(globalThis as unknown as { fetch: unknown }).fetch = jest.fn(() =>
	Promise.resolve({
		ok: true,
		status: 200,
		json: () => Promise.resolve({}),
		text: () => Promise.resolve(''),
	}),
) as jest.Mock

// Mock import.meta for tests - using values from .env.test but keeping test mode settings
Object.defineProperty(globalThis, 'import', {
	value: {
		meta: {
			env: {
				MODE: 'test',
				VITE_API_BASE_URL: 'http://localhost:8080',
				VITE_DEBUG_HTTP: 'false',
				VITE_DATA_REFRESH_RATE: '8000',
				VITE_USE_MSW: 'true',
			},
		},
	},
	writable: true,
})

// Mock environment variables for tests - will be handled by individual test files

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

beforeAll(() => {
	console.error = (...args: unknown[]) => {
		if (
			typeof args[0] === 'string' &&
			(args[0].includes('Warning: ReactDOM.render is no longer supported') ||
				args[0].includes('Warning: useLayoutEffect does nothing on the server'))
		) {
			return
		}
		originalConsoleError.call(console, ...args)
	}

	console.warn = (...args: unknown[]) => {
		if (
			typeof args[0] === 'string' &&
			args[0].includes('Warning: componentWillReceiveProps has been renamed')
		) {
			return
		}
		originalConsoleWarn.call(console, ...args)
	}
})

afterAll(() => {
	console.error = originalConsoleError
	console.warn = originalConsoleWarn
})
