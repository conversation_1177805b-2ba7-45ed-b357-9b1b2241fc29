import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import {
	type TaskStateColorSettings,
	DEFAULT_TASK_STATE_COLORS,
} from '../../common/utils/taskStateColors'
import type { Task } from '../../models/Task'

interface AppState {
	locale: 'en' | 'ja'
	setLocale: (l: 'en' | 'ja') => void
	themeMode: 'light' | 'dark'
	setThemeMode: (m: 'light' | 'dark') => void
	toggleThemeMode: () => void
	fontScale: number
	setFontScale: (s: number) => void
	taskStateColors: TaskStateColorSettings
	setTaskStateColors: (colors: TaskStateColorSettings) => void
	autoRefresh: boolean
	setAutoRefresh: (enabled: boolean) => void

	// Task management
	tasksByOperator: Record<string, Task[]>
	setTasksForOperator: (operatorName: string, tasks: Task[]) => void
	getTasksForOperator: (operatorName: string) => Task[]
	updateTask: (operatorName: string, taskId: number, updates: Partial<Task>) => void
	clearTasksForOperator: (operatorName: string) => void
	clearAllTasks: () => void
}

// Safe localStorage wrapper with error handling
const createSafeStorage = () => {
	const isStorageAvailable = () => {
		try {
			const test = '__storage_test__'
			localStorage.setItem(test, test)
			localStorage.removeItem(test)
			return true
		} catch {
			return false
		}
	}

	if (!isStorageAvailable()) {
		console.warn('localStorage is not available, falling back to memory storage')
		// Fallback to memory storage
		const memoryStorage = new Map<string, string>()
		return {
			getItem: (name: string) => memoryStorage.get(name) ?? null,
			setItem: (name: string, value: string) => {
				memoryStorage.set(name, value)
			},
			removeItem: (name: string) => {
				memoryStorage.delete(name)
			},
		}
	}

	return {
		getItem: (name: string) => {
			try {
				return localStorage.getItem(name)
			} catch (error) {
				console.error('Error reading from localStorage:', error)
				return null
			}
		},
		setItem: (name: string, value: string) => {
			try {
				localStorage.setItem(name, value)
			} catch (error) {
				console.error('Error writing to localStorage:', error)
			}
		},
		removeItem: (name: string) => {
			try {
				localStorage.removeItem(name)
			} catch (error) {
				console.error('Error removing from localStorage:', error)
			}
		},
	}
}

export const useAppStore = create<AppState>()(
	persist(
		(set, get) => ({
			locale: 'en',
			setLocale: (locale) => set({ locale }),
			themeMode: 'light',
			setThemeMode: (themeMode) => set({ themeMode }),
			toggleThemeMode: () =>
				set({ themeMode: get().themeMode === 'light' ? 'dark' : 'light' }),
			fontScale: 1,
			setFontScale: (fontScale) => set({ fontScale }),
			taskStateColors: DEFAULT_TASK_STATE_COLORS,
			setTaskStateColors: (taskStateColors) => set({ taskStateColors }),
			autoRefresh: false,
			setAutoRefresh: (autoRefresh) => set({ autoRefresh }),

			// Task management
			tasksByOperator: {},
			setTasksForOperator: (operatorName: string, tasks: Task[]) =>
				set((state) => ({
					tasksByOperator: {
						...state.tasksByOperator,
						[operatorName]: tasks,
					},
				})),
			getTasksForOperator: (operatorName: string) => {
				const state = get()
				return state.tasksByOperator[operatorName] || []
			},
			updateTask: (operatorName: string, taskId: number, updates: Partial<Task>) =>
				set((state) => {
					const operatorTasks = state.tasksByOperator[operatorName] || []
					const updatedTasks = operatorTasks.map((task) =>
						task.id === taskId ? { ...task, ...updates } : task,
					)
					return {
						tasksByOperator: {
							...state.tasksByOperator,
							[operatorName]: updatedTasks,
						},
					}
				}),
			clearTasksForOperator: (operatorName: string) =>
				set((state) => {
					const { [operatorName]: _, ...rest } = state.tasksByOperator
					return { tasksByOperator: rest }
				}),
			clearAllTasks: () => set({ tasksByOperator: {} }),
		}),
		{
			name: 'vam-app-settings', // unique name for localStorage key
			storage: createJSONStorage(() => createSafeStorage()),
			// Only persist the settings we want to save (exclude tasks)
			partialize: (state) => ({
				locale: state.locale,
				themeMode: state.themeMode,
				fontScale: state.fontScale,
				taskStateColors: state.taskStateColors,
				autoRefresh: state.autoRefresh,
				// tasksByOperator is intentionally excluded from persistence
			}),
			// Handle migration for future schema changes
			version: 1,
			migrate: (persistedState: unknown, version: number) => {
				if (version === 0) {
					// Migration logic for future versions
					return persistedState
				}
				return persistedState
			},
			// Skip hydration during SSR
			skipHydration: false,
		},
	),
)
