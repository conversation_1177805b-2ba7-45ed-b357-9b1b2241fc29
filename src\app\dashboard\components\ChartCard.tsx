import {
	Paper,
	Typography,
	useTheme,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Box,
	IconButton,
	Tooltip,
	Divider,
} from '@mui/material'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import {
	Bar,
	Bar<PERSON>hart,
	CartesianGrid,
	ResponsiveContainer,
	Tooltip as Recharts<PERSON>ooltip,
	<PERSON>Axis,
	<PERSON>Axis,
} from 'recharts'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'
import { getAlertColors } from '../../../common/utils/alertColors'

interface ChartCardProps {
	selectedTask: Task | null
	onFullView?: (viewType: 'table' | 'chart', title?: string) => void
}

function areTasksEqual(a: Task | null, b: Task | null): boolean {
	if (a === b) return true
	if (!a || !b) return a === b
	return (
		a.id === b.id &&
		a.rowName === b.rowName &&
		a.name === b.name &&
		a.progressRate === b.progressRate &&
		a.plannedStart === b.plannedStart &&
		a.plannedEnd === b.plannedEnd &&
		a.plannedDuration === b.plannedDuration &&
		a.actualStart === b.actualStart &&
		a.actualEnd === b.actualEnd &&
		a.actualDuration === b.actualDuration
	)
}

function ChartCardComponent({ selectedTask, onFullView }: ChartCardProps) {
	const theme = useTheme()
	const { t } = useTranslation()

	if (!selectedTask) {
		return (
			<Paper className="p-3 sm:p-4">
				<Typography variant="subtitle1" color="text.secondary">
					Select a task to view details
				</Typography>
			</Paper>
		)
	}

	const alertColors = getAlertColors(selectedTask.alerts)
	const data = [{ name: selectedTask.name, value: selectedTask.progressRate }]

	const timeData = [
		{
			type: 'Planned',
			start: selectedTask.plannedStart,
			end: selectedTask.plannedEnd,
			duration: selectedTask.plannedDuration,
		},
		{
			type: 'Actual',
			start: selectedTask.actualStart,
			end: selectedTask.actualEnd,
			duration: selectedTask.actualDuration,
		},
	]

	return (
		<Paper
			className="h-full p-3 sm:p-4 space-y-3 sm:space-y-4 flex flex-col"
		>
			{selectedTask ? <Box sx={{ display: 'flex', alignItems: 'flex-end', margin: 0, padding: 0 }}>
				<Typography variant="subtitle1" fontWeight={600}>
					{selectedTask.vangp}
				</Typography>
				<Typography
					variant="body1"
					fontWeight={600}
					sx={{
						ml: 1,
						color: getAlertColors(selectedTask.alerts).progressRate,
					}}
				>
					{t('chart.progress')}: {selectedTask.progressRate}%
				</Typography>
				{onFullView ? (
					<Tooltip title={t('common.fullscreen') ?? 'Fullscreen'} arrow>
						<IconButton
							size="small"
							aria-label="fullscreen chart"
							onClick={() =>
								onFullView('chart', `${selectedTask.name} - Detailed View`)
							}
							className="justify-end self-center"
							sx={{ color: 'text.secondary', ml: 'auto' }}
						>
							<FullscreenIcon fontSize="small" />
						</IconButton>
					</Tooltip>
				) : null}
			</Box> : null}
			<Divider />
			<TableContainer>
				<Table sx={{
					backgroundColor: '#fff', '& .MuiTableHead-root': {
						backgroundColor: getAlertColors(selectedTask.alerts).header,
					},
					'& .MuiTableRow-root': {
						backgroundColor: getAlertColors(selectedTask.alerts).row,
					},
				}}>
					<TableHead>
						<TableRow>
							{[
								selectedTask.name,
								t('table.start'),
								t('table.end')
							].map((label, idx) => {
								return (<TableCell
									key={idx}
									align="center"
									sx={{
										fontWeight: 600,
										backgroundColor: alertColors.header,
										color: '#fff',
										cursor: 'default',
									}}
								>{label}</TableCell>)
							})}
						</TableRow>
					</TableHead>
					<TableBody>
						{timeData.map((row, idx) => (
							<TableRow key={idx}>
								{[row.type, row.start, row.end].map((data, idx) => {
									return (<TableCell key={idx} align="center" sx={{ cursor: 'default' }}>{data}</TableCell>)
								})}
							</TableRow>
						))}
					</TableBody>
					<TableBody>

					</TableBody>
				</Table>
			</TableContainer>
			<Box className="flex-1 justify-center w-full">
				<ResponsiveContainer width="100%" height="100%">
					<BarChart data={data} margin={{ top: 16, right: 16, left: 16, bottom: 16 }}>
						<CartesianGrid stroke={theme.palette.divider} strokeDasharray="3 3" />
						<XAxis
							dataKey="name"
							stroke={theme.palette.text.secondary}
							fontSize={12}
							tick={{ fill: theme.palette.text.secondary }}
						/>
						<YAxis
							domain={[0, 100]}
							tickFormatter={(value) => `${value}%`}
							stroke={theme.palette.text.secondary}
							fontSize={12}
							tick={{ fill: theme.palette.text.secondary }}
						/>
						<RechartsTooltip
							formatter={(value) => [`${value}%`, 'Progress']}
							contentStyle={{
								backgroundColor: theme.palette.background.paper,
								border: `1px solid ${theme.palette.divider}`,
								borderRadius: 8,
								color: theme.palette.text.primary,
							}}
						/>
						<Bar
							dataKey="value"
							fill={getAlertColors(selectedTask.alerts).background}
							radius={[4, 4, 0, 0]}
							stroke={getAlertColors(selectedTask.alerts).header}
							strokeWidth={1}
						/>
					</BarChart>
				</ResponsiveContainer>
			</Box>
		</Paper>
	)
}

const ChartCard = memo(ChartCardComponent, (prev, next) =>
	areTasksEqual(prev.selectedTask, next.selectedTask),
)

export default ChartCard
