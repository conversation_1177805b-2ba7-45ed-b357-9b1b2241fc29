# Contributing

Welcome to the VMA Web Application project! This guide will help you contribute effectively to the codebase.

## Prerequisites

Before contributing, ensure you have:

- Node.js 18+ (LTS recommended)
- npm 10+
- Git configured with your name and email
- Code editor with TypeScript support (VS Code recommended)

## Getting Started

1. **Clone the repository:**

    ```bash
    git clone <repository-url>
    cd vma-web
    ```

2. **Install dependencies:**

    ```bash
    npm install
    ```

3. **Start development server:**

    ```bash
    npm run dev
    ```

4. **Run tests to ensure everything works:**
    ```bash
    npm test
    ```

## Branching Strategy

### Branch Types

- **`main`**: Protected branch, always release-ready and stable
- **`feat/<scope>-<short-desc>`**: New features (e.g., `feat/dashboard-fullscreen-view`)
- **`fix/<scope>-<short-desc>`**: Bug fixes (e.g., `fix/api-timeout-handling`)
- **`docs/<short-desc>`**: Documentation updates (e.g., `docs/contributing-guide`)
- **`chore/<short-desc>`**: Maintenance tasks (e.g., `chore/update-dependencies`)

### Workflow

1. **Create a new branch from main:**

    ```bash
    git checkout main
    git pull origin main
    git checkout -b feat/your-feature-name
    ```

2. **Work on your changes:**

    ```bash
    # Make your changes
    git add .
    git commit -m "add: implement feature functionality"
    ```

3. **Push your branch:**
    ```bash
    git push origin feat/your-feature-name
    ```

## Commit Guidelines

### Commit Message Format

Use clear, imperative messages that describe what the commit does:

**Good examples:**

```bash
git commit -m "add fullscreen view for chart card"
git commit -m "fix API timeout handling in HTTP client"
git commit -m "update contributing documentation"
git commit -m "refactor dashboard component structure"
```

**Bad examples:**

```bash
git commit -m "changes"
git commit -m "fixed stuff"
git commit -m "working on feature"
```

### Conventional Commits (Recommended)

Follow the conventional commit format for better changelog generation:

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

**Types:**

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**

```bash
git commit -m "feat(dashboard): add fullscreen view for charts"
git commit -m "fix(api): handle timeout errors gracefully"
git commit -m "docs(contributing): update release process"
```

### Commit Best Practices

1. **Keep commits small and focused** - One logical change per commit
2. **Group related changes** - Don't mix unrelated changes in one commit
3. **Write descriptive messages** - Future you will thank you
4. **Test before committing** - Ensure your changes work
5. **Use present tense** - "add feature" not "added feature"

## Pull Requests

### Creating a Pull Request

1. **Ensure your branch is up to date:**

    ```bash
    git checkout main
    git pull origin main
    git checkout your-feature-branch
    git rebase main  # or git merge main
    ```

2. **Run all checks locally:**

    ```bash
    npm run typecheck  # Check TypeScript types
    npm run lint       # Check code style
    npm test          # Run all tests
    npm run build     # Ensure build works
    ```

3. **Push your final changes:**

    ```bash
    git push origin your-feature-branch
    ```

4. **Create the PR on GitHub/GitLab** with a descriptive title and description

### PR Requirements

- **One feature/fix per PR** - Keep PRs focused and reviewable
- **Descriptive title** - Clearly state what the PR does
- **Detailed description** - Explain the changes, why they were made, and how to test
- **Screenshots/GIFs** - For UI changes, include visual evidence
- **Testing notes** - Explain how reviewers can test the changes
- **Documentation updates** - Update relevant docs if needed
- **All checks passing** - CI/build must pass, no lint/type errors

### PR Template

```markdown
## Description

Brief description of what this PR does.

## Changes Made

- List of specific changes
- Another change
- etc.

## Testing

- [ ] Unit tests added/updated
- [ ] Manual testing completed
- [ ] All existing tests pass

## Screenshots (if applicable)

[Add screenshots for UI changes]

## Breaking Changes

[List any breaking changes or mark as N/A]

## Checklist

- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Tests added for new functionality
- [ ] Documentation updated
- [ ] No console errors or warnings
```

## Code Style Guidelines

### TypeScript Standards

- **Strict TypeScript** - Avoid `any` type, use proper typing
- **Interface over type** - Prefer interfaces for object shapes
- **Explicit return types** - For functions, especially public APIs
- **Proper generics** - Use generics for reusable components

**Good examples:**

```typescript
// Good - explicit types
interface User {
	id: string
	name: string
	email: string
}

function getUser(id: string): Promise<User> {
	return fetchUser(id)
}

// Bad - using any
function getUser(id: any): any {
	return fetchUser(id)
}
```

### Code Organization

- **Meaningful names** - Use descriptive variable and function names
- **Early returns** - Reduce nesting with guard clauses
- **Shallow nesting** - Keep code readable with minimal indentation
- **Single responsibility** - Functions should do one thing well
- **DRY principle** - Don't repeat yourself

**Example:**

```typescript
// Good - early return, clear naming
function validateUserEmail(email: string): boolean {
	if (!email) return false
	if (!email.includes('@')) return false
	if (email.length < 5) return false
	return true
}

// Bad - nested conditions
function validateUserEmail(email: string): boolean {
	if (email) {
		if (email.includes('@')) {
			if (email.length >= 5) {
				return true
			}
		}
	}
	return false
}
```

### Formatting and Linting

- **Prettier** - Automatic code formatting (configured in `.prettierrc`)
- **EditorConfig** - Consistent editor settings across team
- **ESLint** - Code quality and style rules
- **Pre-commit hooks** - Automatic formatting and linting

**Commands:**

```bash
npm run format    # Format code with Prettier
npm run lint      # Check for linting issues
npm run lint:fix  # Auto-fix linting issues
```

### React Best Practices

- **Functional components** - Use function components over class components
- **Custom hooks** - Extract reusable logic into custom hooks
- **Props interface** - Define clear interfaces for component props
- **Memoization** - Use `React.memo`, `useMemo`, `useCallback` appropriately
- **Error boundaries** - Handle errors gracefully

**Example:**

```typescript
interface ChartCardProps {
  task: Task
  onFullscreen: () => void
  isFullscreen: boolean
}

const ChartCard: React.FC<ChartCardProps> = React.memo(({
  task,
  onFullscreen,
  isFullscreen
}) => {
  const chartData = useMemo(() =>
    processTaskData(task), [task]
  )

  const handleClick = useCallback(() => {
    onFullscreen()
  }, [onFullscreen])

  return (
    <Card>
      {/* Component JSX */}
    </Card>
  )
})
```

## Testing Guidelines

### Testing Strategy

- **Unit tests** - Test individual functions and components
- **Integration tests** - Test component interactions and API calls
- **End-to-end tests** - Test complete user workflows (when applicable)
- **Test coverage** - Maintain good coverage for new features
- **Test both scenarios** - Success and error cases

### Testing Tools

- **Jest** - Test runner and assertion library
- **React Testing Library** - Component testing utilities
- **MSW (Mock Service Worker)** - API mocking for tests
- **Testing Library User Events** - Simulate user interactions

### Writing Tests

**Component Testing Example:**

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { ChartCard } from '../ChartCard'
import { mockTask } from '../../__mocks__/mockData'

describe('ChartCard', () => {
  const mockProps = {
    task: mockTask,
    onFullscreen: jest.fn(),
    isFullscreen: false
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render task information correctly', () => {
    render(<ChartCard {...mockProps} />)

    expect(screen.getByText(mockTask.name)).toBeInTheDocument()
    expect(screen.getByText(`${mockTask.progress}%`)).toBeInTheDocument()
  })

  it('should call onFullscreen when fullscreen button is clicked', () => {
    render(<ChartCard {...mockProps} />)

    const fullscreenButton = screen.getByRole('button', { name: /fullscreen/i })
    fireEvent.click(fullscreenButton)

    expect(mockProps.onFullscreen).toHaveBeenCalledTimes(1)
  })

  it('should handle error states gracefully', () => {
    const errorProps = { ...mockProps, task: null }
    render(<ChartCard {...errorProps} />)

    expect(screen.getByText(/no data available/i)).toBeInTheDocument()
  })
})
```

**API Testing Example:**

```typescript
import { httpGet } from '../httpClient'
import { server } from '../../mocks/server'
import { rest } from 'msw'

describe('httpGet', () => {
	it('should fetch data successfully', async () => {
		const mockData = { id: 1, name: 'Test' }

		server.use(
			rest.get('/api/test', (req, res, ctx) => {
				return res(ctx.json(mockData))
			}),
		)

		const result = await httpGet('/api/test')
		expect(result).toEqual(mockData)
	})

	it('should handle errors properly', async () => {
		server.use(
			rest.get('/api/test', (req, res, ctx) => {
				return res(ctx.status(500))
			}),
		)

		await expect(httpGet('/api/test')).rejects.toThrow('HTTP 500')
	})
})
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test ChartCard.test.tsx

# (powershell) Run tests matching pattern
npm test -- -- --testNamePattern="DataTable Component"

# (command prompt) Run tests matching pattern
npm test -- --testNamePattern="should render"
```

### Test Requirements

- **New features** - Must include tests for new functionality
- **Bug fixes** - Add regression tests to prevent future issues
- **Component changes** - Update existing tests as needed
- **API changes** - Test both success and error scenarios
- **Coverage** - Maintain reasonable test coverage (aim for >80%)

## Code Quality Checklist

### Pre-Commit Checks

Before committing your changes, ensure:

```bash
# 1. Format code
npm run format

# 2. Check for linting issues
npm run lint

# 3. Fix auto-fixable lint issues
npm run lint:fix

# 4. Check TypeScript types
npm run typecheck

# 5. Run all tests
npm test

# 6. Ensure build works
npm run build
```

### Quality Standards

- **No linting errors** - Code must pass ESLint checks
- **No TypeScript errors** - All types must be correct
- **All tests passing** - No failing tests allowed
- **Consistent formatting** - Use Prettier for formatting
- **No console errors** - Clean browser console
- **Performance** - No unnecessary re-renders or memory leaks

### Code Review Checklist

- [ ] Code follows project conventions
- [ ] Functions are small and focused
- [ ] Variable names are descriptive
- [ ] No hardcoded values (use constants/config)
- [ ] Error handling is implemented
- [ ] Tests cover new functionality
- [ ] Documentation is updated
- [ ] No security vulnerabilities introduced

## Versioning & Releases (Changesets)

This project uses [Changesets](https://github.com/changesets/changesets) for version management and automated releases.

### Understanding Changesets

Changesets help us:

- Track which packages need version bumps
- Generate changelogs automatically
- Follow semantic versioning
- Coordinate releases across the team

### Step-by-Step Release Process

#### 1. Create a Changeset (After Making Changes)

When you've completed your feature/fix and are ready to commit:

```bash
# Create a changeset
npm run changeset
```

This will prompt you with:

- **Which packages to bump** (select `vmapp`)
- **Version type** (major, minor, patch)
- **Summary** of your changes

**Example interaction:**

```
? Which packages would you like to include?
  ✓ vmapp

? Which packages should have a major bump?
  (none selected)

? Which packages should have a minor bump?
  ✓ vmapp

? Which packages should have a patch bump?
  (none selected)

? Please enter a summary for this change:
  Add fullscreen view for chart cards with enhanced data visualization
```

#### 2. Commit Your Changes

```bash
# Add all changes including the changeset
git add .
git commit -m "feat(dashboard): add fullscreen view for chart cards"
git push origin your-feature-branch
```

#### 3. Create Pull Request

Create a PR as described in the Pull Requests section above.

#### 4. After PR is Merged (Maintainer Steps)

Once your PR is merged to main, a maintainer will handle the release:

```bash
# Switch to main and pull latest
git checkout main
git pull origin main

# Generate version bump and changelog
npm run changeset:version

# This creates/updates:
# - package.json version
# - CHANGELOG.md
# - Removes consumed changeset files

# Commit the version changes
git add .
git commit -m "chore: version bump and changelog update"
git push origin main

# Create and push git tag
git tag v1.2.0  # Use the new version number
git push origin v1.2.0

# Publish the release (if applicable)
npm run changeset:publish
```

### Semantic Versioning Guide

- **MAJOR (1.0.0 → 2.0.0)** - Breaking changes that require user action
- **MINOR (1.0.0 → 1.1.0)** - New features that are backward compatible
- **PATCH (1.0.0 → 1.0.1)** - Bug fixes and small improvements

**Examples:**

- Adding new component → **MINOR**
- Fixing a bug → **PATCH**
- Changing API interface → **MAJOR**
- Adding new feature → **MINOR**

### Your Current Changeset

You mentioned you have a changeset ready. Here's what to do next:

```bash
# 1. Check your current changeset
ls .changeset/
# You should see: loud-geckos-stop.md

# 2. Review the changeset content
cat .changeset/loud-geckos-stop.md
# Should show:
# ---
# 'vmapp': minor
# ---
# Feature add fullscreen view for data table and chart view in the element card

# 3. Commit your changes (if not already done)
git add .
git commit -m "feat(dashboard): add fullscreen view for data table and chart"
git push origin your-branch-name

# 4. Create a Pull Request
# Go to GitHub/GitLab and create a PR from your branch to main

# 5. After PR is merged, maintainer will run:
npm run changeset:version  # Updates version and changelog
npm run changeset:publish  # Publishes the release
```

## Documentation Guidelines

### What to Document

- **README/SETUP changes** - Update if workflows change
- **Environment variables** - Document new vars in `src/core/config/env.ts` and README
- **API changes** - Keep API documentation current
- **Complex functions** - Add JSDoc comments
- **Component props** - Document interfaces and usage

### JSDoc Examples

```typescript
/**
 * Renders a chart card with fullscreen capability
 * @param task - The task data to display
 * @param onFullscreen - Callback when fullscreen is toggled
 * @param isFullscreen - Whether the card is in fullscreen mode
 * @returns React component
 */
interface ChartCardProps {
	/** Task data containing progress and time information */
	task: Task
	/** Callback function triggered when fullscreen button is clicked */
	onFullscreen: () => void
	/** Current fullscreen state */
	isFullscreen: boolean
}
```

### Documentation Files to Update

- `README.md` - Project overview and setup
- `SETUP.md` - Development setup instructions
- `ENVIRONMENT_SETUP.md` - Environment configuration
- `CONTRIBUTING.md` - This file
- Component README files (if applicable)

## Review Process

### PR Review Requirements

- **At least one review** - All PRs require approval
- **CI checks passing** - Build, tests, and linting must pass
- **No merge conflicts** - Resolve conflicts before review
- **Complete description** - Clear explanation of changes

### Review Checklist for Reviewers

- [ ] Code follows project standards
- [ ] Tests are adequate and passing
- [ ] Documentation is updated
- [ ] No security issues introduced
- [ ] Performance impact considered
- [ ] Breaking changes documented
- [ ] UI/UX changes are intuitive

### Addressing Review Comments

1. **Read carefully** - Understand the feedback
2. **Ask questions** - If unclear, ask for clarification
3. **Make changes** - Address the feedback
4. **Respond** - Comment on what you changed
5. **Re-request review** - When ready for another look

### Merging Guidelines

- **Squash commits** - For feature branches with multiple commits
- **Merge commits** - For complex features that benefit from history
- **Rebase** - For simple changes to maintain linear history

## Common Workflows

### Adding a New Feature

1. Create feature branch: `git checkout -b feat/new-feature`
2. Implement the feature with tests
3. Run quality checks: `npm run lint && npm test && npm run build`
4. Create changeset: `npm run changeset`
5. Commit changes: `git commit -m "feat: add new feature"`
6. Push branch: `git push origin feat/new-feature`
7. Create Pull Request
8. Address review feedback
9. Merge after approval

### Fixing a Bug

1. Create fix branch: `git checkout -b fix/bug-description`
2. Write failing test (if applicable)
3. Fix the bug
4. Ensure test passes
5. Create changeset: `npm run changeset` (select patch)
6. Commit and push
7. Create Pull Request

### Updating Documentation

1. Create docs branch: `git checkout -b docs/update-contributing`
2. Make documentation changes
3. Review for accuracy and clarity
4. No changeset needed for docs-only changes
5. Commit and create PR

## Getting Help

- **Questions about code** - Ask in team chat or create GitHub issue
- **Build/setup issues** - Check SETUP.md or ask for help
- **Review feedback** - Don't hesitate to ask for clarification
- **Changeset questions** - Refer to [Changesets documentation](https://github.com/changesets/changesets)

## Quick Reference

### Essential Commands

```bash
# Development
npm run dev          # Start dev server
npm run dev:mock     # Start with MSW mocking

# Quality checks
npm run lint         # Check linting
npm run typecheck    # Check TypeScript
npm test            # Run tests
npm run build       # Build for production

# Changesets
npm run changeset           # Create changeset
npm run changeset:version   # Bump versions
npm run changeset:publish   # Publish release

# Formatting
npm run format      # Format code with Prettier
```

### File Structure

```
src/
├── app/                    # Feature modules
│   ├── dashboard/         # Dashboard feature
│   └── settings/          # Settings feature
├── common/                # Shared components & hooks
├── core/                  # Infrastructure
│   ├── api/              # HTTP client & API calls
│   ├── config/           # Environment configuration
│   └── i18n/             # Internationalization
├── mocks/                 # MSW mock handlers
└── pages/                 # Page components
```

---

**Happy contributing! 🎉**

If you have questions or need help, don't hesitate to ask the team.
