import { Suspense, lazy, useState, useEffect } from 'react'
import { Paper, type PaperProps } from '@mui/material'
import FullscreenModal from '../../../common/components/FullscreenModal'
import DataTable from './DataTable'
import FullscreenView from './FullscreenView'
import type { Operator } from '../../../models/Operator'
import type { Task } from '../../../models/Task'
import { useTasks } from '../hooks/useTasks'

const LazyChartCard = lazy(() => import('./ChartCard'))

interface ElementCardProps extends PaperProps {
	operator: Operator
}



const ElementCard = ({ operator, ...paperProps }: ElementCardProps) => {
	const { tasks } = useTasks({ operatorName: operator.name })
	const initial = null
	const [selected, setSelected] = useState<Task | null>(initial)

	// Update selected task when tasks change
	useEffect(() => {
		if (tasks.length > 0) {
			if (selected) {
				const latest = tasks.find((t) => t.id === selected.id)
				if (latest && latest !== selected) {
					setSelected(latest)
				} else if (!latest) {
					// Selected task is no longer in the array, clear selection
					setSelected(null)
				}
			}
		} else {
			// No tasks available, clear selection
			setSelected(null)
		}
	}, [tasks, selected])

	const [fsOpen, setFsOpen] = useState(false)
	const [fsTitle, setFsTitle] = useState<string | undefined>(undefined)
	const [fsViewType, setFsViewType] = useState<'table' | 'chart'>('table')

	const openFullscreen = (viewType: 'table' | 'chart', title?: string) => {
		setFsViewType(viewType)
		setFsTitle(title)
		setFsOpen(true)
	}



	return (
		<Paper
			variant="outlined"
			className={`space-y-4 w-full h-full`}
			{...paperProps}
		>
			<div className='h-full grid grid-cols-2 gap-[0.25rem]'>
				<div className="min-h-64 md:min-h-72">
					<DataTable
						operatorName={operator.name}
						selectedRowId={selected?.id ?? null}
						onSelect={(row) => setSelected(row)}
						onFullView={openFullscreen}
					/>
				</div>
				<div className="min-h-64 md:min-h-72">
					<Suspense fallback={<div className="p-4">Loading chart…</div>}>
						<LazyChartCard selectedTask={selected} onFullView={openFullscreen} />
					</Suspense>
				</div>
			</div>
			<FullscreenModal open={fsOpen} title={fsTitle} onClose={() => setFsOpen(false)}>
				{fsOpen && (
					<FullscreenView
						operator={operator}
						initialTasks={tasks}
						initialSelectedTask={selected}
						viewType={fsViewType}
						onTaskSelect={setSelected}
					/>
				)}
			</FullscreenModal>
		</Paper>
	)
}

export default ElementCard
