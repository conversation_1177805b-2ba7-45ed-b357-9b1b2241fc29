import React, { useState } from 'react'
import { Box, Paper, TextField, Typography } from '@mui/material'

interface ColorInputProps {
	label: string
	value: string
	onChange: (value: string) => void
}

export const ColorInput: React.FC<ColorInputProps> = ({
	label, value, onChange
}) => {
	const [inputValue, setInputValue] = useState(value)

	const handleChange = (newValue: string) => {
		setInputValue(newValue)
		// Validate hex color format
		if (/^#[0-9A-Fa-f]{6}$/.test(newValue)) {
			onChange(newValue)
		}
	}

	const handleBlur = () => {
		// Reset to valid value if invalid
		if (!/^#[0-9A-Fa-f]{6}$/.test(inputValue)) {
			setInputValue(value)
		}
	}

	return (
		<Paper sx={{ mb: 2, border: '1px solid #ccc', borderRadius: 1, p: 1 }}>
			<Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
				<Box
					data-testid={`color-preview-${label.toLowerCase().replace(/\s+/g, '-')}`}
					sx={{
						width: '1.5rem',
						height: '1rem',
						backgroundColor: value,
						border: '1px solid #222',
						borderRadius: 0.5,
						flexShrink: 0,
					}}
				/>
				<Typography variant="overline">
					{label}
				</Typography>
			</Box>
			<TextField
				size="small"
				value={inputValue}
				onChange={(e) => handleChange(e.target.value)}
				onBlur={handleBlur}
				placeholder="#FFFFFF"
				slotProps={{
					htmlInput: {
						pattern: '^#[0-9A-Fa-f]{6}$',
						maxLength: 7,
					},
				}}
			/>
		</Paper>
	)
}