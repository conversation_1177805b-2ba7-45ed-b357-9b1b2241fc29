/**
 * Centralized validation constants for form validation
 * 
 * These constants should match the backend validation rules defined in:
 * - vma-api/src/main/java/com/inspeedia/vanning/dto/SignupRequest.java
 * - vma-api/src/main/java/com/inspeedia/vanning/dto/LoginRequest.java
 * - vma-api/src/main/java/com/inspeedia/vanning/domain/User.java
 */

export const VALIDATION_RULES = {
	username: {
		minLength: 3,
		maxLength: 50,
		required: true,
		pattern: /^[a-zA-Z0-9_-]+$/,
		errorMessages: {
			required: 'Username is required',
			minLength: 'Username must be at least 3 characters',
			maxLength: 'Username must not exceed 50 characters',
			pattern: 'Username can only contain letters, numbers, underscores, and hyphens',
		},
	},
	password: {
		minLength: 6,
		maxLength: 100,
		required: true,
		errorMessages: {
			required: 'Password is required',
			minLength: 'Password must be at least 6 characters',
			maxLength: 'Password must not exceed 100 characters',
			mismatch: 'Passwords do not match',
		},
	},
	fullName: {
		minLength: 1,
		maxLength: 100,
		required: true,
		errorMessages: {
			required: 'Full name is required',
			maxLength: 'Full name must not exceed 100 characters',
		},
	},
} as const

/**
 * Validate username according to rules
 */
export function validateUsername(username: string): string | null {
	if (!username || username.trim().length === 0) {
		return VALIDATION_RULES.username.errorMessages.required
	}
	if (username.length < VALIDATION_RULES.username.minLength) {
		return VALIDATION_RULES.username.errorMessages.minLength
	}
	if (username.length > VALIDATION_RULES.username.maxLength) {
		return VALIDATION_RULES.username.errorMessages.maxLength
	}
	if (!VALIDATION_RULES.username.pattern.test(username)) {
		return VALIDATION_RULES.username.errorMessages.pattern
	}
	return null
}

/**
 * Validate password according to rules
 */
export function validatePassword(password: string): string | null {
	if (!password || password.length === 0) {
		return VALIDATION_RULES.password.errorMessages.required
	}
	if (password.length < VALIDATION_RULES.password.minLength) {
		return VALIDATION_RULES.password.errorMessages.minLength
	}
	if (password.length > VALIDATION_RULES.password.maxLength) {
		return VALIDATION_RULES.password.errorMessages.maxLength
	}
	return null
}

/**
 * Validate password confirmation
 */
export function validatePasswordConfirmation(password: string, confirmPassword: string): string | null {
	if (password !== confirmPassword) {
		return VALIDATION_RULES.password.errorMessages.mismatch
	}
	return null
}

/**
 * Validate full name according to rules
 */
export function validateFullName(fullName: string): string | null {
	if (!fullName || fullName.trim().length === 0) {
		return VALIDATION_RULES.fullName.errorMessages.required
	}
	if (fullName.length > VALIDATION_RULES.fullName.maxLength) {
		return VALIDATION_RULES.fullName.errorMessages.maxLength
	}
	return null
}

