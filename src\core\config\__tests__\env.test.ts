import { getApiBaseUrl, getDebugHttp, getDataRefreshRate, ENV } from '../env'

describe('Environment Configuration', () => {
	describe('Basic functionality', () => {
		it('should be able to import and use env functions without TypeScript errors', async () => {
			const apiBaseUrl = await getApiBaseUrl()
			const debugHttp = await getDebugHttp()
			const dataRefreshRate = await getDataRefreshRate()

			expect(typeof apiBaseUrl).toBe('string')
			expect(typeof debugHttp).toBe('boolean')
			expect(typeof dataRefreshRate).toBe('number')
		})

		it('should return expected values in test environment', async () => {
			const apiBaseUrl = await getApiBaseUrl()
			const debugHttp = await getDebugHttp()
			const dataRefreshRate = await getDataRefreshRate()

			// In test environment, should use mocked values from setupTests.ts
			expect(apiBaseUrl).toBe('http://localhost:8080')
			expect(debugHttp).toBe(false)
			expect(dataRefreshRate).toBe(8000)
		})

		it('should provide backward compatibility with ENV object', () => {
			expect(ENV).toBeDefined()
			expect(typeof ENV.API_BASE_URL).toBe('string')
			expect(typeof ENV.DEBUG_HTTP).toBe('boolean')
			expect(typeof ENV.DATA_REFRESH_RATE).toBe('number')
		})
	})

	describe('Environment variable access', () => {
		it('should handle different environment modes', async () => {
			// Test that the functions work regardless of environment
			const apiBaseUrl = await getApiBaseUrl()
			const debugHttp = await getDebugHttp()
			const dataRefreshRate = await getDataRefreshRate()

			// Should not throw errors and return valid types
			expect(apiBaseUrl).toBeDefined()
			expect(debugHttp).toBeDefined()
			expect(dataRefreshRate).toBeDefined()
			expect(dataRefreshRate).toBeGreaterThan(0)
		})

		it('should handle missing environment variables gracefully', async () => {
			// Even if some env vars are missing, should provide defaults
			const dataRefreshRate = await getDataRefreshRate()
			expect(dataRefreshRate).toBeGreaterThan(0)
		})
	})

	describe('Production mode simulation', () => {
		it('should handle production mode configuration', async () => {
			// Mock production environment
			const originalGlobal = (globalThis as any).import

			try {
				// Simulate production mode
				;(globalThis as any).import = {
					meta: {
						env: {
							MODE: 'production',
							VITE_API_BASE_URL: '',
							VITE_DEBUG_HTTP: 'false',
							VITE_DATA_REFRESH_RATE: '7000',
						},
					},
				}

				const apiBaseUrl = await getApiBaseUrl()
				const debugHttp = await getDebugHttp()
				const dataRefreshRate = await getDataRefreshRate()

				// In production, should handle config.json loading gracefully
				expect(typeof apiBaseUrl).toBe('string')
				expect(typeof debugHttp).toBe('boolean')
				expect(typeof dataRefreshRate).toBe('number')
			} finally {
				// Restore original global
				;(globalThis as any).import = originalGlobal
			}
		})
	})
})
