interface AlertColors {
	background: string
	header: string
	row: string
	progressRate: string
}

export const getAlertColors = (alerts: number[] | number): AlertColors => {
	// Check if alerts is an array and contains critical values (1 or 2)
	const hasAlert = Array.isArray(alerts)
		? alerts.some((alert) => alert === 1 || alert === 2)
		: alerts === 1

	if (hasAlert) {
		return {
			background: '#9C27B0', // Purple base
			header: '#7B1FA2', // Darker purple for header
			row: '#E1BEE7', // Light purple for rows
			progressRate: '#e6172f',
		}
	}

	return {
		background: '#2196F3', // Blue base
		header: '#1976D2', // Darker blue for header
		row: '#FFF', // Light blue for rows
		progressRate: '#00e676',
	}
}
