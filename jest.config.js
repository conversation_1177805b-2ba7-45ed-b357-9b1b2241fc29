export default {
	preset: 'ts-jest',
	testEnvironment: 'jsdom',
	setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
	setupFiles: ['<rootDir>/src/jest.d.ts'],
	// Set environment to test mode
	testEnvironmentOptions: {
		customExportConditions: ['node', 'node-addons'],
	},
	globals: {
		'import.meta': {
			env: {
				MODE: 'test',
				DEV: true,
				PROD: false,
			},
		},
	},
	moduleNameMapper: {
		'^@/(.*)$': '<rootDir>/src/$1',
	},
	transform: {
		'^.+\\.tsx?$': [
			'ts-jest',
			{
				useESM: true,
				tsconfig: {
					jsx: 'react-jsx',
					esModuleInterop: true,
					types: ['jest', '@testing-library/jest-dom', 'node'],
					lib: ['ES2022', 'DOM', 'DOM.Iterable'],
					target: 'ES2022',
					module: 'ESNext',
					moduleResolution: 'bundler',
					skipLibCheck: true,
					strict: true,
					allowSyntheticDefaultImports: true,
				},
			},
		],
	},
	extensionsToTreatAsEsm: ['.ts', '.tsx'],
	moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
	testMatch: [
		'<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
		'<rootDir>/src/**/*.{test,spec}.{ts,tsx}',
	],
	collectCoverageFrom: [
		'src/**/*.{ts,tsx}',
		'!src/**/*.d.ts',
		'!src/main.tsx',
		'!src/index.css',
		'!src/vite-env.d.ts',
	],
	coverageDirectory: 'coverage',
	coverageReporters: ['text', 'lcov', 'html'],
}
