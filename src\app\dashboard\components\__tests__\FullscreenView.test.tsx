/**
 * Tests for FullscreenView component
 */

import { render, screen, waitFor } from '@testing-library/react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import FullscreenView from '../FullscreenView'
import { mockTasks } from '../../../../__mocks__/mockData'
import type { Operator } from '../../../../models/Operator'

// Mock the services
jest.mock('../../services/tasks', () => ({
	fetchTasksForOperator: jest.fn(),
}))

// Import the mocked service
import { fetchTasksForOperator } from '../../services/tasks'
const mockFetchTasksForOperator = fetchTasksForOperator as jest.MockedFunction<
	typeof fetchTasksForOperator
>

// Mock the env config
jest.mock('../../../../core/config/env', () => ({
	getDataRefreshRate: jest.fn().mockResolvedValue(5000),
}))

// Mock the child components
jest.mock('../FullscreenDataTable', () => {
	return function MockFullscreenDataTable({
		rows,
		selectedRowId,
		onSelect,
	}: {
		rows: any[]
		selectedRowId: number | null
		onSelect?: (task: any) => void
	}) {
		return (
			<div data-testid="fullscreen-data-table">
				<div>Rows: {rows.length}</div>
				<div>Selected: {selectedRowId}</div>
				{rows.map((row) => (
					<button key={row.id} onClick={() => onSelect?.(row)}>
						{row.name}
					</button>
				))}
			</div>
		)
	}
})

jest.mock('../FullscreenChartCard', () => {
	return function MockFullscreenChartCard({ selectedTask }: { selectedTask: any }) {
		return (
			<div data-testid="fullscreen-chart-card">
				<div>Task: {selectedTask?.name || 'None'}</div>
			</div>
		)
	}
})

// Mock react-i18next
jest.mock('react-i18next', () => ({
	useTranslation: () => ({
		t: (key: string) => key,
	}),
}))

const theme = createTheme()

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
	<ThemeProvider theme={theme}>{children}</ThemeProvider>
)

const mockOperator: Operator = {
	name: 'TestOperator',
	tasks: mockTasks,
}

describe('FullscreenView', () => {
	const defaultProps = {
		operator: mockOperator,
		initialTasks: mockTasks,
		initialSelectedTask: mockTasks[0],
		viewType: 'table' as const,
		onTaskSelect: jest.fn(),
	}

	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe('table view', () => {
		it('should render FullscreenDataTable when viewType is table', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} viewType="table" />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByTestId('fullscreen-data-table')).toBeInTheDocument()
			expect(screen.getByText(`Rows: ${mockTasks.length}`)).toBeInTheDocument()
			expect(screen.getByText(`Selected: ${mockTasks[0].id}`)).toBeInTheDocument()
		})

		it('should pass correct props to FullscreenDataTable', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} viewType="table" />
				</TestWrapper>,
			)

			// Assert
			mockTasks.forEach((task) => {
				expect(screen.getByText(task.name)).toBeInTheDocument()
			})
		})

		it('should handle task selection in table view', () => {
			// Arrange
			const mockOnTaskSelect = jest.fn()
			render(
				<TestWrapper>
					<FullscreenView
						{...defaultProps}
						viewType="table"
						onTaskSelect={mockOnTaskSelect}
					/>
				</TestWrapper>,
			)

			// Act
			const taskButton = screen.getByText(mockTasks[1].name)
			taskButton.click()

			// Assert
			expect(mockOnTaskSelect).toHaveBeenCalledWith(mockTasks[1])
		})
	})

	describe('chart view', () => {
		it('should render FullscreenChartCard when viewType is chart and task is selected', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} viewType="chart" />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByTestId('fullscreen-chart-card')).toBeInTheDocument()
			expect(screen.getByText(`Task: ${mockTasks[0].name}`)).toBeInTheDocument()
		})

		it('should show fallback message when viewType is chart but no task is selected', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} viewType="chart" initialSelectedTask={null} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByText('No task selected for chart view')).toBeInTheDocument()
			expect(screen.queryByTestId('fullscreen-chart-card')).not.toBeInTheDocument()
		})
	})

	describe('data refresh', () => {
		it('should set up data refresh interval', async () => {
			// Arrange
			mockFetchTasksForOperator.mockResolvedValue(mockTasks)

			// Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} />
				</TestWrapper>,
			)

			// Assert
			await waitFor(() => {
				expect(fetchTasksForOperator).toHaveBeenCalledWith(
					mockOperator.name,
					expect.any(AbortSignal),
				)
			})
		})

		it('should handle fetch errors gracefully', async () => {
			// Arrange
			const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation()
			mockFetchTasksForOperator.mockRejectedValue(new Error('Network error'))

			// Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} />
				</TestWrapper>,
			)

			// Assert
			await waitFor(() => {
				expect(consoleErrorSpy).toHaveBeenCalledWith(
					'Failed to fetch tasks in fullscreen:',
					expect.any(Error),
				)
			})

			consoleErrorSpy.mockRestore()
		})

		it('should not log abort errors', async () => {
			// Arrange
			const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation()
			const abortError = new DOMException('Aborted', 'AbortError')
			mockFetchTasksForOperator.mockRejectedValue(abortError)

			// Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} />
				</TestWrapper>,
			)

			// Assert
			await waitFor(() => {
				expect(fetchTasksForOperator).toHaveBeenCalled()
			})

			// Should not log abort errors
			expect(consoleErrorSpy).not.toHaveBeenCalled()

			consoleErrorSpy.mockRestore()
		})
	})

	describe('task selection updates', () => {
		it('should update selected task when tasks change', async () => {
			// Arrange
			const updatedTask = { ...mockTasks[0], name: 'Updated Task' }
			const updatedTasks = [updatedTask, ...mockTasks.slice(1)]

			mockFetchTasksForOperator.mockResolvedValue(updatedTasks)

			// Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} viewType="chart" />
				</TestWrapper>,
			)

			// Assert
			await waitFor(() => {
				expect(screen.getByText('Task: Updated Task')).toBeInTheDocument()
			})
		})

		it('should clear selection when selected task is no longer available', async () => {
			// Arrange
			const tasksWithoutSelected = mockTasks.slice(1) // Remove first task

			mockFetchTasksForOperator.mockResolvedValue(tasksWithoutSelected)

			// Act
			render(
				<TestWrapper>
					<FullscreenView {...defaultProps} viewType="chart" />
				</TestWrapper>,
			)

			// Assert
			await waitFor(() => {
				expect(screen.getByText('No task selected for chart view')).toBeInTheDocument()
			})
		})
	})

	describe('cleanup', () => {
		it('should cleanup intervals and abort controllers on unmount', () => {
			// Arrange
			const { unmount } = render(
				<TestWrapper>
					<FullscreenView {...defaultProps} />
				</TestWrapper>,
			)

			// Act
			unmount()

			// Assert - No errors should be thrown during cleanup
			expect(true).toBe(true) // Test passes if no errors during unmount
		})
	})
})
