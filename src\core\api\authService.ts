import { httpPost, httpGet } from './httpClient'
import { endpoints } from './endpoints'
import { setToken, removeToken, getToken, hasToken, getUserFromToken } from '../utils/tokenStorage'

export interface LoginRequest {
	username: string
	password: string
}

export interface SignupRequest {
	username: string
	password: string
	fullName: string
}

export interface PasswordResetRequest {
	username: string
	newPassword: string
}

export interface LoginResponse {
	success: boolean
	message: string
	username?: string
	fullName?: string
	token?: string
}

/**
 * Login with username and password
 */
export async function login(credentials: LoginRequest): Promise<LoginResponse> {
	const response = await httpPost<LoginResponse>(endpoints.auth.login, credentials)

	// Store JWT token if login successful
	if (response.success && response.token) {
		setToken(response.token)
	}

	return response
}

/**
 * Logout current user
 */
export async function logout(): Promise<LoginResponse> {
	const response = await httpPost<LoginResponse>(endpoints.auth.logout)

	// Remove token regardless of server response
	removeToken()

	return response
}

/**
 * Check authentication status
 */
export async function checkAuthStatus(): Promise<LoginResponse> {
	return await httpGet<LoginResponse>(endpoints.auth.status)
}

/**
 * Check if user is authenticated (has valid token)
 */
export function isAuthenticated(): boolean {
	return hasToken()
}

/**
 * Get current user info from stored token
 */
export function getCurrentUser(): { username: string; fullName: string; userId: number } | null {
	const token = getToken()
	if (!token) {
		return null
	}
	return getUserFromToken(token)
}

/**
 * Sign up a new user
 */
export async function signup(signupData: SignupRequest): Promise<LoginResponse> {
	const response = await httpPost<LoginResponse>(endpoints.auth.signup, signupData)
	return response
}

/**
 * Reset user password
 */
export async function resetPassword(resetData: PasswordResetRequest): Promise<LoginResponse> {
	const response = await httpPost<LoginResponse>(endpoints.auth.resetPassword, resetData)
	return response
}
