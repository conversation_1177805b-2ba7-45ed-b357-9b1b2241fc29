import React from 'react'
import { render } from '@testing-library/react'
import type { RenderOptions } from '@testing-library/react'
import { ThemeProvider } from '@mui/material/styles'
import { createTheme } from '@mui/material/styles'
import { I18nextProvider } from 'react-i18next'
import i18n from 'i18next'

// Create a test i18n instance
const testI18n = i18n.createInstance()
testI18n.init({
	lng: 'en',
	fallbackLng: 'en',
	resources: {
		en: {
			translation: {
				'table.name': 'Name',
				'table.planned': 'Planned',
				'table.actual': 'Actual',
				'table.no': 'No',
				'table.date': 'Date',
				'table.shippingDate': 'Shipping Date',
				'table.vangp': 'Vangp',
				'table.deliveryTime': 'Delivery Time',
				'table.start': 'Start',
				'table.end': 'End',
				'table.duration': 'Duration',
				'table.type': 'Type',
				'chart.progress': 'Progress',
			},
		},
	},
	interpolation: {
		escapeValue: false,
	},
})

// Create a test theme
const testTheme = createTheme({
	palette: {
		mode: 'light',
		primary: {
			main: '#1976d2',
		},
		secondary: {
			main: '#dc004e',
		},
	},
})

// Custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
	theme?: typeof testTheme
}

function customRender(ui: React.ReactElement, options: CustomRenderOptions = {}) {
	const { theme = testTheme, ...renderOptions } = options

	const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
		return (
			<I18nextProvider i18n={testI18n}>
				<ThemeProvider theme={theme}>{children}</ThemeProvider>
			</I18nextProvider>
		)
	}

	return render(ui, { wrapper: AllTheProviders, ...renderOptions })
}

// Re-export everything
// eslint-disable-next-line react-refresh/only-export-components
export * from '@testing-library/react'

// Override render method
export { customRender as render }

// Export test utilities
export { testTheme }
