import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { copyFileSync, mkdirSync } from 'fs'
import { resolve } from 'path'

// Plugin to copy locale files during build
function copyLocalesPlugin() {
	return {
		name: 'copy-locales',
		writeBundle() {
			// Ensure the locales directory exists in dist
			mkdirSync('dist/locales', { recursive: true })

			// Copy locale files from source to dist
			copyFileSync(resolve('src/core/i18n/locales/en.json'), resolve('dist/locales/en.json'))
			copyFileSync(resolve('src/core/i18n/locales/ja.json'), resolve('dist/locales/ja.json'))

			console.log('✓ Locale files copied to dist/locales/')
		},
	}
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
	// Log the current mode with proper descriptions
	const modeDescriptions = {
		dev: '🔧 Development mode (using .env.dev)',
		mock: '🎭 Mock development mode (using .env.mock with MSW)',
		test: '🧪 Test mode (using .env.test)',
		production: '🚀 Production mode (using config.json only)',
	}

	const description =
		modeDescriptions[mode as keyof typeof modeDescriptions] || `🔧 Building in ${mode} mode`
	console.log(description)

	return {
		plugins: [react(), copyLocalesPlugin()],
		publicDir: 'public',
		build: {
			outDir: 'dist',
			assetsDir: 'assets',
		},
		server: {
			// Serve locale files during development
			fs: {
				allow: ['..'],
			},
		},
		// Ensure environment variables are properly loaded
		envDir: '.',
		envPrefix: 'VITE_',
	}
})
