import type { Task } from '../../../models/Task'
import { httpGet } from '../../../core/api/httpClient'
import { endpoints } from '../../../core/api/endpoints'

export async function fetchTasks(signal?: AbortSignal): Promise<Task[]> {
	return httpGet<Task[]>(endpoints.tasksToday, {
		signal,
		headers: { 'cache-control': 'no-cache' },
	})
}

export async function fetchTasksForOperator(
	operatorName: string,
	signal?: AbortSignal,
): Promise<Task[]> {
	return httpGet<Task[]>(
		endpoints.tasksTodayForOperator.replace('{operatorName}', encodeURIComponent(operatorName)),
		{
			signal,
			headers: { 'cache-control': 'no-cache' },
		},
	)
}
