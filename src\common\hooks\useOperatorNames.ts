import { useEffect, useState } from 'react'
import { getOperatorNamesManager, type OperatorDto } from '../../core/api/operatorService'

/**
 * Hook to manage operator names with automatic retry logic
 * 
 * This hook provides:
 * - Automatic fetching of operator names
 * - Continuous retry every second when the list is empty
 * - Proper cleanup on component unmount
 * - Manual refresh capability
 */
export function useOperatorNames() {
	const [operators, setOperators] = useState<OperatorDto[]>([])
	const [isLoading, setIsLoading] = useState(true)
	const [error, setError] = useState<string | null>(null)

	useEffect(() => {
		const manager = getOperatorNamesManager()
		
		// Subscribe to operator names updates
		const unsubscribe = manager.subscribe((newOperators) => {
			setOperators(newOperators)
			setIsLoading(false)
			
			// Clear error if we successfully got data
			if (newOperators.length > 0) {
				setError(null)
			}
		})

		// Cleanup subscription on unmount
		return unsubscribe
	}, [])

	/**
	 * Manually refresh operator names
	 */
	const refresh = () => {
		setIsLoading(true)
		setError(null)
		const manager = getOperatorNamesManager()
		manager.refresh()
	}

	return {
		operators,
		isLoading,
		error,
		refresh,
		isEmpty: operators.length === 0,
	}
}
