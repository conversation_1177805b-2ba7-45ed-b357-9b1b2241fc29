/**
 * Tests for TaskStateColorPicker component
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import TaskStateColorPicker from '../TaskStateColorPicker'
import { DEFAULT_TASK_STATE_COLORS } from '../../../../common/utils/taskStateColors'

// Mock react-i18next
jest.mock('react-i18next', () => ({
	useTranslation: () => ({
		t: (key: string, options?: { defaultValue?: string }) => options?.defaultValue || key,
	}),
}))

const theme = createTheme()

const renderWithTheme = (component: React.ReactElement) => {
	return render(<ThemeProvider theme={theme}>{component}</ThemeProvider>)
}

describe('TaskStateColorPicker', () => {
	const mockOnChange = jest.fn()
	const mockOnReset = jest.fn()

	beforeEach(() => {
		jest.clearAllMocks()
	})

	const defaultProps = {
		colors: DEFAULT_TASK_STATE_COLORS,
		onChange: mockOnChange,
		onReset: mockOnReset,
	}

	it('should render all color inputs', () => {
		renderWithTheme(<TaskStateColorPicker {...defaultProps} />)

		expect(screen.getByText('Normal')).toBeInTheDocument()
		expect(screen.getByText('Working')).toBeInTheDocument()
		expect(screen.getByText('Completed')).toBeInTheDocument()
		expect(screen.getByText('Delay')).toBeInTheDocument()
		expect(screen.getByText('Interrupt')).toBeInTheDocument()
	})

	it('should display current color values', () => {
		renderWithTheme(<TaskStateColorPicker {...defaultProps} />)

		const inputs = screen.getAllByDisplayValue(/#[0-9A-Fa-f]{6}/)
		expect(inputs).toHaveLength(5)

		// Check that default colors are displayed
		expect(screen.getByDisplayValue('#FFFFFF')).toBeInTheDocument() // normal
		expect(screen.getByDisplayValue('#FFFF00')).toBeInTheDocument() // working
		expect(screen.getByDisplayValue('#D0D0D0')).toBeInTheDocument() // completed
		expect(screen.getByDisplayValue('#FF0000')).toBeInTheDocument() // delay
		expect(screen.getByDisplayValue('#CAEDFB')).toBeInTheDocument() // interrupt
	})

	it('should show color preview boxes', () => {
		renderWithTheme(<TaskStateColorPicker {...defaultProps} />)

		// Check that color preview boxes are rendered by looking for Box elements with specific styling
		const colorBoxes = screen.getAllByTestId(/color-preview-/)
		expect(colorBoxes).toHaveLength(5)
	})

	it('should call onChange when valid color is entered', async () => {
		renderWithTheme(<TaskStateColorPicker {...defaultProps} />)

		const normalInput = screen.getByDisplayValue('#FFFFFF')
		fireEvent.change(normalInput, { target: { value: '#123456' } })

		await waitFor(() => {
			expect(mockOnChange).toHaveBeenCalledWith({
				...DEFAULT_TASK_STATE_COLORS,
				normal: '#123456',
			})
		})
	})

	it('should not call onChange for invalid color format', async () => {
		renderWithTheme(<TaskStateColorPicker {...defaultProps} />)

		const normalInput = screen.getByDisplayValue('#FFFFFF')
		fireEvent.change(normalInput, { target: { value: 'invalid' } })

		// Wait a bit to ensure onChange is not called
		await new Promise((resolve) => setTimeout(resolve, 100))
		expect(mockOnChange).not.toHaveBeenCalled()
	})

	it('should reset input value on blur if invalid', async () => {
		renderWithTheme(<TaskStateColorPicker {...defaultProps} />)

		const normalInput = screen.getByDisplayValue('#FFFFFF')
		fireEvent.change(normalInput, { target: { value: 'invalid' } })
		fireEvent.blur(normalInput)

		await waitFor(() => {
			expect(normalInput).toHaveValue('#FFFFFF')
		})
	})

	it('should render title and description', () => {
		renderWithTheme(<TaskStateColorPicker {...defaultProps} />)

		expect(screen.getByText('Table Row Colors')).toBeInTheDocument()
		expect(screen.getByText(/These colors only apply to table rows/)).toBeInTheDocument()
	})

	it('should handle custom colors prop', () => {
		const customColors = {
			normal: '#111111',
			working: '#222222',
			completed: '#333333',
			delay: '#444444',
			interrupt: '#555555',
		}

		renderWithTheme(<TaskStateColorPicker {...defaultProps} colors={customColors} />)

		expect(screen.getByDisplayValue('#111111')).toBeInTheDocument()
		expect(screen.getByDisplayValue('#222222')).toBeInTheDocument()
		expect(screen.getByDisplayValue('#333333')).toBeInTheDocument()
		expect(screen.getByDisplayValue('#444444')).toBeInTheDocument()
		expect(screen.getByDisplayValue('#555555')).toBeInTheDocument()
	})

	it('should validate hex color format correctly', async () => {
		renderWithTheme(<TaskStateColorPicker {...defaultProps} />)

		const normalInput = screen.getByDisplayValue('#FFFFFF')

		// Test valid formats
		fireEvent.change(normalInput, { target: { value: '#123456' } })
		await waitFor(() => {
			expect(mockOnChange).toHaveBeenCalledWith({
				...DEFAULT_TASK_STATE_COLORS,
				normal: '#123456',
			})
		})

		mockOnChange.mockClear()

		fireEvent.change(normalInput, { target: { value: '#ABCDEF' } })
		await waitFor(() => {
			expect(mockOnChange).toHaveBeenCalledWith({
				...DEFAULT_TASK_STATE_COLORS,
				normal: '#ABCDEF',
			})
		})

		mockOnChange.mockClear()

		// Test invalid formats
		const invalidFormats = ['#12345', '#1234567', 'FFFFFF', 'red', '#GGG']

		for (const invalid of invalidFormats) {
			fireEvent.change(normalInput, { target: { value: invalid } })
			await new Promise((resolve) => setTimeout(resolve, 50))
		}

		expect(mockOnChange).not.toHaveBeenCalled()
	})
})
