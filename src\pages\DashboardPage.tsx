import { useEffect, useState } from 'react'
import { Box, Pagination, Typography, useTheme } from '@mui/material'
import { ElementCard } from '../app/dashboard'
import { useOperatorNames } from '../common/hooks/useOperatorNames'
import type { Operator } from '../models/Operator'
import TableLegends from '../common/components/TableLegends'

const elementsPerPagePortrait = 4
const elementsPerPageLandscape = 4

export default function DashboardPage() {
	const theme = useTheme()
	const [page, setPage] = useState(1)
	const [isLandscape, setIsLandscape] = useState(
		() => matchMedia('(orientation: landscape)').matches,
	)

	// Use the new operator names hook with retry logic
	const { operators: operatorDtos, isLoading: loading, error } = useOperatorNames()

	// Convert OperatorDto[] to Operator[] for compatibility
	const operators: Operator[] = (operatorDtos || []).map((dto) => ({ name: dto.name, tasks: [] }))

	useEffect(() => {
		const mq = matchMedia('(orientation: landscape)')
		const handler = () => setIsLandscape(mq.matches)
		mq.addEventListener?.('change', handler)
		return () => mq.removeEventListener?.('change', handler)
	}, [])

	const elementsPerPage = isLandscape ? elementsPerPageLandscape : elementsPerPagePortrait
	const totalElements = operators.length
	const totalPages = Math.ceil(totalElements / elementsPerPage)
	const startIndex = (page - 1) * elementsPerPage
	const pageItems = Array.from(
		{ length: Math.min(elementsPerPage, totalElements - startIndex) },
		(_, i) => startIndex + i,
	)

	if (loading) {
		return (
			<Box className="p-3 sm:p-4 w-full">
				<Typography>Loading…</Typography>
			</Box>
		)
	}
	if (error) {
		return (
			<Box className="p-3 sm:p-4 w-full">
				<Typography color="error">{error}</Typography>
			</Box>
		)
	}

	// Estimate heights for sticky header & bar to compute scrollable area height
	// Use theme spacing units (8px base) and dynamic viewport height for better mobile behavior
	// const headerUnits = 5 // 5 * 8 = 40px
	// const stickyBarUnits = 7 // 7 * 8 = 56px
	// const verticalPadUnits = 2 // 2 * 8 = 16px
	// const totalOffset = theme.spacing(headerUnits + stickyBarUnits + verticalPadUnits)
	// const maxHCalc = `calc(100dvh - ${totalOffset})`

	return (
		<Box
			className="space-y-4 w-full"
			sx={{ height: 'calc(100dvh - 60px)', display: 'flex', flexDirection: 'column' }}
		>
			<Box
				className="sticky top-0 z-10"
				sx={{
					backgroundColor: theme.palette.background.paper,
					borderBottom: `1px solid ${theme.palette.divider}`,
					boxShadow:
						theme.palette.mode === 'light'
							? '0 1px 2px rgba(0,0,0,0.04)'
							: '0 1px 2px rgba(0,0,0,0.5)',
					pt: 1,
					pb: 1,
					mb: '0.25rem',
					flexShrink: 0,
				}}
			>
				<div className="flex justify-center">
					<Pagination
						count={totalPages}
						page={page}
						onChange={(_, p) => setPage(p)}
						color="primary"
					/>
				</div>
			</Box>

			{/* Table Legends */}
			<Box sx={{ px: { xs: 1, sm: 2, md: 3 }, mb: '0.25rem', flexShrink: 0 }}>
				<TableLegends compact={true} />
			</Box>

			<Box
				sx={{
					flex: 1, // This makes the Paper take all available space
					display: 'flex',
					justifyContent: 'space-evenly',
					flexDirection: 'column',
					gap: '0.25rem',
					overflow: 'auto',
					pb: { xs: 3, sm: 4 },
				}}
			>
				{pageItems.map((idx) => (
					<ElementCard
						key={idx}
						className="col-span-1 w-full"
						data-item-id={idx}
						operator={operators[idx]}
					/>
				))}
			</Box>
		</Box>
	)
}
