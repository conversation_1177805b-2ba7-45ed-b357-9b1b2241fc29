/**
 * Tests for task state colors utility functions
 */

import {
	TaskState,
	TASK_STATE_COLORS,
	DEFAULT_TASK_STATE_COLORS,
	validateTaskStateColors,
	getTaskState,
	getTaskStateColor,
	getTaskStateColors,
} from '../taskStateColors'
import type { Task } from '../../../models/Task'

describe('Task State Colors', () => {
	const mockTask: Task = {
		id: 1,
		name: 'Test Task',
		rowName: 'A1',
		date: '2024-01-15',
		shippingDate: '日付',
		vangp: 'AA',
		deliveryTime: '09:00',
		plannedStart: '08:00',
		plannedEnd: '17:00',
		plannedDuration: '9h',
		plannedVs: '17:05',
		actualStart: '',
		actualEnd: '',
		actualDuration: '',
		actualVs: '',
		progress: 0,
		progressRate: 0,
		working: false,
		completed: false,
		alerts: [],
	}

	describe('TaskState enum', () => {
		it('should have correct values', () => {
			expect(TaskState.NORMAL).toBe('normal')
			expect(TaskState.WORKING).toBe('working')
			expect(TaskState.COMPLETED).toBe('completed')
			expect(TaskState.DELAY).toBe('delay')
			expect(TaskState.INTERRUPT).toBe('interrupt')
		})
	})

	describe('TASK_STATE_COLORS', () => {
		it('should have correct color values', () => {
			expect(TASK_STATE_COLORS[TaskState.NORMAL]).toBe('#FFFFFF')
			expect(TASK_STATE_COLORS[TaskState.WORKING]).toBe('#FFFF00')
			expect(TASK_STATE_COLORS[TaskState.COMPLETED]).toBe('#D0D0D0')
			expect(TASK_STATE_COLORS[TaskState.DELAY]).toBe('#FF0000')
			expect(TASK_STATE_COLORS[TaskState.INTERRUPT]).toBe('#CAEDFB')
		})
	})

	describe('validateTaskStateColors', () => {
		it('should return default colors for empty input', () => {
			const result = validateTaskStateColors({})
			expect(result).toEqual(DEFAULT_TASK_STATE_COLORS)
		})

		it('should validate valid hex colors', () => {
			const colors = {
				normal: '#123456',
				working: '#ABCDEF',
				completed: '#000000',
				delay: '#FFFFFF',
				interrupt: '#FF00FF',
			}
			const result = validateTaskStateColors(colors)
			expect(result).toEqual(colors)
		})

		it('should fallback to defaults for invalid colors', () => {
			const colors = {
				normal: 'invalid',
				working: '#GGG',
				completed: '#12345',
				delay: '#1234567',
				interrupt: 'red',
			}
			const result = validateTaskStateColors(colors)
			expect(result).toEqual(DEFAULT_TASK_STATE_COLORS)
		})

		it('should mix valid and invalid colors', () => {
			const colors = {
				normal: '#123456',
				working: 'invalid',
				completed: '#ABCDEF',
			}
			const result = validateTaskStateColors(colors)
			expect(result.normal).toBe('#123456')
			expect(result.working).toBe(DEFAULT_TASK_STATE_COLORS.working)
			expect(result.completed).toBe('#ABCDEF')
			expect(result.delay).toBe(DEFAULT_TASK_STATE_COLORS.delay)
			expect(result.interrupt).toBe(DEFAULT_TASK_STATE_COLORS.interrupt)
		})
	})

	describe('getTaskState', () => {
		it('should return NORMAL for task with no start time', () => {
			const task = { ...mockTask, actualStart: '', actualEnd: '', alerts: [] }
			expect(getTaskState(task)).toBe(TaskState.NORMAL)
		})

		it('should return WORKING for task with start time but no end time', () => {
			const task = { ...mockTask, actualStart: '08:15', actualEnd: '', alerts: [] }
			expect(getTaskState(task)).toBe(TaskState.WORKING)
		})

		it('should return COMPLETED for task with end time', () => {
			const task = { ...mockTask, actualStart: '08:15', actualEnd: '17:30', alerts: [] }
			expect(getTaskState(task)).toBe(TaskState.COMPLETED)
		})

		it('should return DELAY for task with alerts (highest priority)', () => {
			const task = { ...mockTask, actualStart: '08:15', actualEnd: '17:30', alerts: [1] }
			expect(getTaskState(task)).toBe(TaskState.DELAY)
		})

		it('should return DELAY for task with multiple alerts', () => {
			const task = {
				...mockTask,
				actualStart: '08:15',
				actualEnd: '17:30',
				alerts: [0, 1, 2],
			}
			expect(getTaskState(task)).toBe(TaskState.DELAY)
		})

		it('should return NORMAL for task with zero alerts', () => {
			const task = { ...mockTask, actualStart: '', actualEnd: '', alerts: [0] }
			expect(getTaskState(task)).toBe(TaskState.NORMAL)
		})

		it('should handle whitespace in start/end times', () => {
			const task1 = { ...mockTask, actualStart: '  ', actualEnd: '', alerts: [] }
			expect(getTaskState(task1)).toBe(TaskState.NORMAL)

			const task2 = { ...mockTask, actualStart: '08:15', actualEnd: '  ', alerts: [] }
			expect(getTaskState(task2)).toBe(TaskState.WORKING)
		})
	})

	describe('getTaskStateColor', () => {
		const colors = DEFAULT_TASK_STATE_COLORS

		it('should return correct color for each state', () => {
			const normalTask = { ...mockTask, actualStart: '', actualEnd: '', alerts: [] }
			expect(getTaskStateColor(normalTask, colors)).toBe(colors.normal)

			const workingTask = { ...mockTask, actualStart: '08:15', actualEnd: '', alerts: [] }
			expect(getTaskStateColor(workingTask, colors)).toBe(colors.working)

			const completedTask = {
				...mockTask,
				actualStart: '08:15',
				actualEnd: '17:30',
				alerts: [],
			}
			expect(getTaskStateColor(completedTask, colors)).toBe(colors.completed)

			const delayTask = { ...mockTask, actualStart: '08:15', actualEnd: '17:30', alerts: [1] }
			expect(getTaskStateColor(delayTask, colors)).toBe(colors.delay)
		})
	})

	describe('getTaskStateColors', () => {
		const colors = DEFAULT_TASK_STATE_COLORS

		it('should return colors with state-based row color and default header for no alerts', () => {
			const task = { ...mockTask, actualStart: '08:15', actualEnd: '', alerts: [] }
			const result = getTaskStateColors(task, colors)

			expect(result.row).toBe(colors.working)
			expect(result.background).toBe('#2196F3')
			expect(result.header).toBe('#1976D2')
			expect(result.progressRate).toBe('#00e676')
		})

		it('should return colors with state-based row color and alert header for tasks with alerts', () => {
			const task = { ...mockTask, actualStart: '08:15', actualEnd: '17:30', alerts: [1] }
			const result = getTaskStateColors(task, colors)

			expect(result.row).toBe(colors.delay)
			expect(result.background).toBe('#9C27B0')
			expect(result.header).toBe('#7B1FA2')
			expect(result.progressRate).toBe('#e6172f')
		})

		it('should handle alert level 2', () => {
			const task = { ...mockTask, actualStart: '', actualEnd: '', alerts: [2] }
			const result = getTaskStateColors(task, colors)

			expect(result.row).toBe(colors.delay) // Delay state due to alert
			expect(result.background).toBe('#9C27B0')
			expect(result.header).toBe('#7B1FA2')
		})

		it('should handle mixed alert levels', () => {
			const task = { ...mockTask, actualStart: '', actualEnd: '', alerts: [0, 1] }
			const result = getTaskStateColors(task, colors)

			expect(result.row).toBe(colors.delay)
			expect(result.background).toBe('#9C27B0')
			expect(result.header).toBe('#7B1FA2')
		})
	})
})
