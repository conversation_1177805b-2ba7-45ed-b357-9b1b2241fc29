import React from 'react'
import {
	Box,
	Paper,
	Typography,
	Slider,
	Divider,
	Button,
	FormControl,
	NativeSelect,
} from '@mui/material'
import RestartAltIcon from '@mui/icons-material/RestartAlt'
import { useTranslation } from 'react-i18next'
import TaskStateColorPicker from '../components/TaskStateColorPicker'
import {
	type TaskStateColorSettings
} from '../../../common/utils/taskStateColors'


interface AccessibilitySettingsProps {
	fontScale: number
	onFontScaleChange: (value: number) => void
	taskStateColors: TaskStateColorSettings
	handleTaskStateColorsChange: (colors: TaskStateColorSettings) => void
	handleResetSettings: () => void
	locale: 'en' | 'ja'
	handleLocaleChange: (value: 'en' | 'ja') => void
}

export const AccessibilitySettings: React.FC<AccessibilitySettingsProps> = ({
	fontScale,
	onFontScaleChange,
	taskStateColors,
	handleTaskStateColorsChange,
	handleResetSettings,
	locale,
	handleLocaleChange,
}) => {
	const { t } = useTranslation();
	const [fontScaleValue, setFontScaleValue] = React.useState<number>(fontScale)

	const languageOptions = [
		{ value: 'en', label: 'English' },
		{ value: 'ja', label: '日本語' },
	]

	return (
		<Box sx={{ p: 1, mb: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
			<Typography variant="h6">
				{t('settings.accessibility', { defaultValue: 'Accessibility' })}
			</Typography>

			<Paper sx={{ p: 1.5, mb: 1, border: '1px solid #ccc', borderRadius: 1, display: 'flex', flexDirection: 'column' }}>
				<TaskStateColorPicker
					colors={taskStateColors}
					onChange={handleTaskStateColorsChange}
				/>

				<Divider sx={{ my: 2 }} />

				<Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', gap: 1 }}>
					<FormControl sx={{ minWidth: 200 }}>
						<Typography variant="subtitle1" sx={{ mb: 1 }} fontWeight={600} fontSize={'1rem'}>
							{t('settings.language', { defaultValue: 'Language' })}
						</Typography>
						<NativeSelect
							defaultValue={locale}
							onChange={(e) => handleLocaleChange(e.target.value as 'en' | 'ja')}
							inputProps={{
								'aria-label': 'language',
								name: 'locale',
								id: 'locale-select',
							}}
						>
							{languageOptions.map((option) => (
								<option key={option.value} value={option.value}>
									{option.label}
								</option>
							))}
						</NativeSelect>
					</FormControl>
				</Box>

				<Divider sx={{ my: 2 }} />

				<Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
					<Typography variant="subtitle1" fontWeight={600} fontSize={'1rem'}>
						{t('settings.fontScale', { defaultValue: 'Font scale' })} : {(fontScale * 100).toFixed(0)}%
					</Typography>
					<Slider
						value={fontScaleValue}
						min={0.85}
						max={1.3}
						step={0.05}
						valueLabelDisplay="auto"
						marks
						onChange={(_, v) => setFontScaleValue(Array.isArray(v) ? v[0] : v)}
						sx={{ maxWidth: '100rem', width: '20rem' }}
					/>
					<Button variant="contained" onClick={() => onFontScaleChange(fontScaleValue)} sx={{ alignSelf: 'flex-start' }}>
						{t('settings.apply', { defaultValue: 'Apply' })}
					</Button>
				</Box>

				<Button
					variant="outlined"
					startIcon={<RestartAltIcon sx={{ marginTop: 0.5, marginBottom: 0.5 }} />}
					onClick={handleResetSettings}
					color="warning"
					sx={{ mt: '2rem' }}
				>
					{t('settings.reset', { defaultValue: 'Reset Settings to Defaults' })}
				</Button>
			</Paper>
		</Box>
	);
}