/**
 * Tests for DataTable component
 */

import { render, screen, fireEvent } from '../../../../test-utils'
import DataTable from '../DataTable'
import { mockTasks, mockEmptyTasks } from '../../../../__mocks__/mockData'
import { useAppStore } from '../../../../core/store'
import type { Task } from '../../../../models/Task'

// Default mock task state colors
const mockTaskStateColors = {
	normal: '#FFFFFF',
	working: '#FFFF00',
	completed: '#D0D0D0',
	delay: '#FF0000',
	interrupt: '#CAEDFB',
}

// Create a complete mock state that satisfies the AppState interface
const createMockState = (overrides: Partial<any> = {}) => ({
	locale: 'en' as const,
	setLocale: jest.fn(),
	themeMode: 'light' as const,
	setThemeMode: jest.fn(),
	toggleThemeMode: jest.fn(),
	fontScale: 1,
	setFontScale: jest.fn(),
	taskStateColors: mockTaskStateColors,
	setTaskStateColors: jest.fn(),
	autoRefresh: false,
	setAutoRefresh: jest.fn(),
	// Task management
	tasksByOperator: {},
	setTasksForOperator: jest.fn(),
	getTasksForOperator: jest.fn(() => []),
	updateTask: jest.fn(),
	clearTasksForOperator: jest.fn(),
	clearAllTasks: jest.fn(),
	...overrides,
})

// Mock the store
jest.mock('../../../../core/store', () => ({
	useAppStore: jest.fn(),
}))
const mockUseAppStore = useAppStore as jest.MockedFunction<typeof useAppStore>

describe('DataTable Component', () => {
	beforeEach(() => {
		// Reset all mocks before each test
		jest.clearAllMocks()

		// Default mock implementation for all tests - simulate Zustand selector pattern
		mockUseAppStore.mockImplementation((selector) => {
			const mockState = createMockState()
			return selector(mockState)
		})
	})

	describe('when rendering with data', () => {
		it('should display table headers correctly', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByText('Planned')).toBeInTheDocument()
			expect(screen.getByText('Actual')).toBeInTheDocument()
		})

		it('should display sub-headers correctly', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByText('Date')).toBeInTheDocument()
			expect(screen.getByText('Shipping Date')).toBeInTheDocument()
			expect(screen.getByText('Vangp')).toBeInTheDocument()
			expect(screen.getByText('Delivery Time')).toBeInTheDocument()
			expect(screen.getAllByText('Start')).toHaveLength(2) // Planned and Actual sections
			expect(screen.getAllByText('End')).toHaveLength(2) // Planned and Actual sections
			expect(screen.getAllByText('Duration')).toHaveLength(2) // Planned and Actual sections
		})

		it('should display task data rows', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByText('A1')).toBeInTheDocument()
			expect(screen.getByText('A2')).toBeInTheDocument()
			expect(screen.getByText('A3')).toBeInTheDocument()
		})

		it('should display task details correctly', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByText('2024-01-15')).toBeInTheDocument()
			expect(screen.getAllByText('日付')).toHaveLength(3)
			expect(screen.getByText('AA')).toBeInTheDocument()
			expect(screen.getByText('09:00')).toBeInTheDocument()
			expect(screen.getByText('08:00')).toBeInTheDocument()
			expect(screen.getByText('17:00')).toBeInTheDocument()
			expect(screen.getByText('9h')).toBeInTheDocument()
		})
	})

	describe('when rendering with empty data', () => {
		it('should display table structure without data rows', () => {
			// Arrange & Act
			render(<DataTable rows={mockEmptyTasks} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			expect(screen.getAllByRole('row')).toHaveLength(2) // Only header rows
		})
	})

	describe('row selection functionality', () => {
		it('should call onSelect when row is clicked', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			render(<DataTable rows={mockTasks} onSelect={mockOnSelect} />)

			// Act
			const firstRow = screen.getByText('A1').closest('tr')
			fireEvent.click(firstRow!)

			// Assert
			expect(mockOnSelect).toHaveBeenCalledWith(mockTasks[0])
			expect(mockOnSelect).toHaveBeenCalledTimes(1)
		})

		it('should not call onSelect when onSelect is not provided', () => {
			// Arrange
			render(<DataTable rows={mockTasks} />)

			// Act
			const firstRow = screen.getByText('A1').closest('tr')
			fireEvent.click(firstRow!)

			// Assert
			// Should not throw error and should not have pointer cursor
			expect(firstRow).toHaveStyle({ cursor: 'default' })
		})

		it('should highlight selected row', () => {
			// Arrange
			const selectedRowId = 1
			render(<DataTable rows={mockTasks} selectedRowId={selectedRowId} />)

			// Assert
			const selectedRow = screen.getByText('A1').closest('tr')
			expect(selectedRow).toHaveAttribute('aria-selected', 'true')
		})

		it('should not highlight any row when no selection', () => {
			// Arrange
			render(<DataTable rows={mockTasks} />)

			// Assert
			const rows = screen.getAllByRole('row').slice(2) // Exclude header rows
			rows.forEach((row) => {
				expect(row).not.toHaveAttribute('aria-selected', 'true')
			})
		})
	})

	describe('table styling and behavior', () => {
		it('should have proper table structure', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			// Note: MUI Table with size="small" adds specific styling but not necessarily a size class
			// Check for the sticky header class instead which is more reliable
			expect(screen.getByRole('table')).toHaveClass('MuiTable-stickyHeader')
		})

		it('should have proper cursor style when onSelect is provided', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			render(<DataTable rows={mockTasks} onSelect={mockOnSelect} />)

			// Assert
			const firstRow = screen.getByText('A1').closest('tr')
			expect(firstRow).toHaveStyle({ cursor: 'pointer' })
		})
	})

	describe('component memoization', () => {
		it('should not re-render when props are equal', () => {
			// Arrange
			const { rerender } = render(<DataTable rows={mockTasks} selectedRowId={1} />)
			const initialTable = screen.getByRole('table')

			// Act
			rerender(<DataTable rows={mockTasks} selectedRowId={1} />)

			// Assert
			expect(screen.getByRole('table')).toBe(initialTable)
		})

		it('should re-render when rows change', () => {
			// Arrange
			const { rerender } = render(<DataTable rows={mockTasks} />)

			// Act
			rerender(<DataTable rows={mockEmptyTasks} />)

			// Assert
			expect(screen.getAllByRole('row')).toHaveLength(2) // Only header rows
		})

		it('should re-render when selectedRowId changes', () => {
			// Arrange
			const { rerender } = render(<DataTable rows={mockTasks} selectedRowId={1} />)

			// Act
			rerender(<DataTable rows={mockTasks} selectedRowId={2} />)

			// Assert
			const newSelectedRow = screen.getByText('A2').closest('tr')
			expect(newSelectedRow).toHaveAttribute('aria-selected', 'true')
		})
	})

	describe('accessibility', () => {
		it('should have proper table semantics', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			// The table has 13 column headers: 3 main headers (Name, Planned, Actual) + 10 sub-headers
			expect(screen.getAllByRole('columnheader')).toHaveLength(14)
		})

		it('should have proper row selection state', () => {
			// Arrange
			render(<DataTable rows={mockTasks} selectedRowId={1} />)

			// Assert
			const selectedRow = screen.getByText('A1').closest('tr')
			expect(selectedRow).toHaveAttribute('aria-selected', 'true')
		})
	})

	describe('fullscreen functionality', () => {
		it('should display fullscreen button when onFullView is provided', () => {
			// Arrange
			const mockOnFullView = jest.fn()
			render(<DataTable rows={mockTasks} onFullView={mockOnFullView} />)

			// Assert
			expect(screen.getByLabelText('fullscreen table')).toBeInTheDocument()
			expect(screen.getByRole('button', { name: /fullscreen/i })).toBeInTheDocument()
		})

		it('should not display fullscreen button when onFullView is not provided', () => {
			// Arrange
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.queryByLabelText('fullscreen table')).not.toBeInTheDocument()
		})

		it('should call onFullView when fullscreen button is clicked', () => {
			// Arrange
			const mockOnFullView = jest.fn()
			render(<DataTable rows={mockTasks} onFullView={mockOnFullView} />)

			// Act
			const fullscreenButton = screen.getByLabelText('fullscreen table')
			fireEvent.click(fullscreenButton)

			// Assert
			expect(mockOnFullView).toHaveBeenCalledTimes(1)
			expect(mockOnFullView).toHaveBeenCalledWith('table', 'Table')
		})

		it('should have proper tooltip for fullscreen button', () => {
			// Arrange
			const mockOnFullView = jest.fn()
			render(<DataTable rows={mockTasks} onFullView={mockOnFullView} />)

			// Assert
			const fullscreenButton = screen.getByLabelText('fullscreen table')
			// The tooltip is handled by MUI Tooltip component, so we check for the button's presence
			expect(fullscreenButton).toBeInTheDocument()
		})
	})

	describe('edge cases', () => {
		it('should handle single row', () => {
			// Arrange
			const singleTask = [mockTasks[0]]
			render(<DataTable rows={singleTask} />)

			// Assert
			expect(screen.getByText('A1')).toBeInTheDocument()
			expect(screen.getAllByRole('row')).toHaveLength(3) // 2 headers + 1 data row
		})

		it('should handle very long task names', () => {
			// Arrange
			const longNameTask = {
				...mockTasks[0],
				name: 'This is a very long task name that might cause layout issues in the table display',
			}
			render(<DataTable rows={[longNameTask]} />)

			// Assert
			// The task name is displayed in the task number cell (T001), not as a separate name column
			expect(screen.getByText('A1')).toBeInTheDocument()
		})
	})

	describe('auto-refresh functionality', () => {

		it('should auto-select working task when autoRefresh is enabled', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			const operatorName = 'test-operator'
			const tasksWithWorking: Task[] = [
				{ ...mockTasks[0], working: false },
				{ ...mockTasks[1], working: true },
				{ ...mockTasks[2], working: false },
			]

			mockUseAppStore.mockImplementation((selector) => {
				const mockState = createMockState({
					autoRefresh: true,
					tasksByOperator: { [operatorName]: tasksWithWorking }
				})
				return selector(mockState)
			})

			// Act
			render(<DataTable operatorName={operatorName} onSelect={mockOnSelect} />)

			// Assert
			expect(mockOnSelect).toHaveBeenCalledWith(tasksWithWorking[1])
			expect(mockOnSelect).toHaveBeenCalledTimes(1)
		})

		it('should not auto-select when autoRefresh is disabled', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			const tasksWithWorking: Task[] = [
				{ ...mockTasks[0], working: false },
				{ ...mockTasks[1], working: true },
				{ ...mockTasks[2], working: false },
			]

			// Explicitly ensure autoRefresh is false
			mockUseAppStore.mockImplementation((selector) => {
				const mockState = createMockState({ autoRefresh: false })
				return selector(mockState)
			})

			// Act
			render(<DataTable rows={tasksWithWorking} onSelect={mockOnSelect} />)

			// Assert
			expect(mockOnSelect).not.toHaveBeenCalled()
		})

		it('should not auto-select when no onSelect callback is provided', () => {
			// Arrange
			const tasksWithWorking: Task[] = [
				{ ...mockTasks[0], working: false },
				{ ...mockTasks[1], working: true },
				{ ...mockTasks[2], working: false },
			]

			mockUseAppStore.mockImplementation((selector) => {
				const mockState = createMockState({ autoRefresh: true })
				return selector(mockState)
			})

			// Act & Assert - should not throw error
			expect(() => {
				render(<DataTable rows={tasksWithWorking} />)
			}).not.toThrow()
		})

		it('should select first working task when multiple working tasks exist', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			const tasksWithMultipleWorking: Task[] = [
				{ ...mockTasks[0], working: false },
				{ ...mockTasks[1], working: true, id: 100 },
				{ ...mockTasks[2], working: true, id: 200 },
			]

			mockUseAppStore.mockImplementation((selector) => {
				const mockState = createMockState({ autoRefresh: true })
				return selector(mockState)
			})

			// Act
			render(<DataTable rows={tasksWithMultipleWorking} onSelect={mockOnSelect} />)

			// Assert
			expect(mockOnSelect).toHaveBeenCalledWith(tasksWithMultipleWorking[1])
			expect(mockOnSelect).toHaveBeenCalledTimes(1)
		})

		it('should not re-select if working task is already selected', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			const workingTask = { ...mockTasks[1], working: true, id: 100 }
			const tasksWithWorking: Task[] = [
				{ ...mockTasks[0], working: false },
				workingTask,
				{ ...mockTasks[2], working: false },
			]

			mockUseAppStore.mockImplementation((selector) => {
				const mockState = createMockState({ autoRefresh: true })
				return selector(mockState)
			})

			// Act
			render(<DataTable rows={tasksWithWorking} selectedRowId={100} onSelect={mockOnSelect} />)

			// Assert
			expect(mockOnSelect).not.toHaveBeenCalled()
		})

		it('should handle case when no working tasks exist', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			const tasksWithoutWorking: Task[] = [
				{ ...mockTasks[0], working: false },
				{ ...mockTasks[1], working: false },
				{ ...mockTasks[2], working: false },
			]

			mockUseAppStore.mockImplementation((selector) => {
				const mockState = createMockState({ autoRefresh: true })
				return selector(mockState)
			})

			// Act
			render(<DataTable rows={tasksWithoutWorking} onSelect={mockOnSelect} />)

			// Assert
			expect(mockOnSelect).not.toHaveBeenCalled()
		})

		it('should re-select when tasks change and new working task appears', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			const initialTasks: Task[] = [
				{ ...mockTasks[0], working: false },
				{ ...mockTasks[1], working: false },
			]

			mockUseAppStore.mockImplementation((selector) => {
				const mockState = createMockState({ autoRefresh: true })
				return selector(mockState)
			})

			const { rerender } = render(<DataTable rows={initialTasks} onSelect={mockOnSelect} />)

			// Act - update tasks with a working task
			const updatedTasks: Task[] = [
				{ ...mockTasks[0], working: false },
				{ ...mockTasks[1], working: true },
			]
			rerender(<DataTable rows={updatedTasks} onSelect={mockOnSelect} />)

			// Assert
			expect(mockOnSelect).toHaveBeenCalledWith(updatedTasks[1])
		})

		it('should disable row click when autoRefresh is enabled', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			const tasksWithWorking: Task[] = [
				{ ...mockTasks[0], working: false },
				{ ...mockTasks[1], working: true },
			]

			mockUseAppStore.mockImplementation((selector) => {
				const mockState = createMockState({ autoRefresh: true })
				return selector(mockState)
			})

			render(<DataTable rows={tasksWithWorking} onSelect={mockOnSelect} />)

			// Clear the auto-selection call
			mockOnSelect.mockClear()

			// Act - try to click on a row
			const firstRow = screen.getByText('A1').closest('tr')
			if (firstRow) {
				fireEvent.click(firstRow)
			}

			// Assert - manual selection should be disabled
			expect(mockOnSelect).not.toHaveBeenCalled()
		})
	})
})
