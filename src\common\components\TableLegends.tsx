import { Box, Typography, Chip } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { useAppStore } from '../../core/store'
import { TaskState, type TaskStateColorSettings } from '../utils/taskStateColors'

interface TableLegendsProps {
	/**
	 * Whether to show the component in compact mode (smaller spacing and text)
	 */
	compact?: boolean
	/**
	 * Custom color settings to override the default ones
	 */
	colorSettings?: TaskStateColorSettings
}

/**
 * TableLegends component displays a legend showing the meaning of different task state colors
 * Used in data tables to help users understand the color coding system
 */
export default function TableLegends({ compact = false, colorSettings }: TableLegendsProps) {
	const { t } = useTranslation()
	const defaultColorSettings = useAppStore((s) => s.taskStateColors)
	const colors = colorSettings || defaultColorSettings

	const legendItems = [
		{
			key: TaskState.NORMAL,
			color: colors.normal,
			label: t('legends.normal', { defaultValue: 'Normal' }),
			description: t('legends.normalDesc', { defaultValue: 'Not started' }),
		},
		{
			key: TaskState.WORKING,
			color: colors.working,
			label: t('legends.working', { defaultValue: 'Working' }),
			description: t('legends.workingDesc', { defaultValue: 'In progress' }),
		},
		{
			key: TaskState.COMPLETED,
			color: colors.completed,
			label: t('legends.completed', { defaultValue: 'Completed' }),
			description: t('legends.completedDesc', { defaultValue: 'Finished' }),
		},
		{
			key: TaskState.DELAY,
			color: colors.delay,
			label: t('legends.delay', { defaultValue: 'Delay' }),
			description: t('legends.delayDesc', { defaultValue: 'Behind schedule' }),
		},
		{
			key: TaskState.INTERRUPT,
			color: colors.interrupt,
			label: t('legends.interrupt', { defaultValue: 'Interrupt' }),
			description: t('legends.interruptDesc', { defaultValue: 'Interrupted' }),
		},
	]

	return (
		<Box
			sx={{
				display: 'flex',
				flexWrap: 'wrap',
				gap: compact ? 1 : 1.5,
				alignItems: 'center',
				py: compact ? 0.5 : 1,
				px: compact ? 1 : 2,
				backgroundColor: 'background.paper',
				borderRadius: 1,
				border: '1px solid',
				borderColor: 'divider',
			}}
		>
			<Typography
				variant={compact ? 'caption' : 'body2'}
				sx={{
					fontWeight: 600,
					color: 'text.secondary',
					mr: compact ? 0.5 : 1,
				}}
			>
				{t('legends.title', { defaultValue: 'Legend:' })}
			</Typography>
			{legendItems.map((item) => (
				<Box
					key={item.key}
					sx={{
						display: 'flex',
						alignItems: 'center',
						gap: 0.5,
					}}
				>
					<Chip
						size={compact ? 'small' : 'medium'}
						label={item.label}
						variant="outlined"
						sx={{
							backgroundColor: item.color,
							// color: item.color === '#FFFFFF' ? '#000000' : '#FFFFFF',
							color: '#000000',
							fontWeight: 500,
							fontSize: compact ? '0.7rem' : '0.75rem',
							height: compact ? 20 : 24,
							padding: '0.25rem',
							'& .MuiChip-label': {
								px: compact ? 0.75 : 1,
							},
						}}
					/>
					{!compact && (
						<Typography
							variant="caption"
							sx={{
								color: 'text.primary',
								fontSize: '0.7rem',
							}}
						>
							{item.description}
						</Typography>
					)}
				</Box>
			))}
		</Box>
	)
}
