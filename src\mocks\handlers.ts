import { http, HttpResponse } from 'msw'
import type { Task } from '../models/Task'

interface RandomStringOptions {
	length?: number
	secure?: boolean
}

function generateRandomUppercase(options: RandomStringOptions = {}): string {
	const { length = 2, secure = false } = options

	if (secure) {
		const array = new Uint32Array(length)
		window.crypto.getRandomValues(array)
		return Array.from(array, (value) => String.fromCharCode(65 + (value % 26))).join('')
	}

	return Array.from({ length }, () =>
		String.fromCharCode(65 + Math.floor(Math.random() * 26)),
	).join('')
}

function randomTime(baseHour: number) {
	const hour = baseHour + Math.floor(Math.random() * 3)
	const minute = Math.floor(Math.random() * 60)
	return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
}

function makeTasks(n: number): Task[] {
	return Array.from({ length: n }, (_, i) => {
		const date = new Date().toISOString().split('T')[0]
		const shippingDate = '日付'
		const rowName = `${i + 1}`
		const vangp = generateRandomUppercase()
		const deliveryTime = randomTime(12)
		const plannedStart = randomTime(8)
		const plannedEnd = randomTime(12)
		const plannedDuration = `${Math.floor(Math.random() * 4 + 1)}h`
		const plannedVs = randomTime(12)
		const actualStart = randomTime(9)
		const actualEnd = randomTime(13)
		const actualDuration = `${Math.floor(Math.random() * 5 + 1)}h`
		const actualVs = randomTime(13)
		const progress = Math.round(Math.random() * 100)
		const progressRate = parseFloat((Math.random() * 100).toFixed(2))
		const working = (progress > 0 && progress < 100)
		const completed =  (progress == 100)
		const alerts = [Math.floor(Math.random() * 3)] // Random alert level
		return {
			id: i + 1,
			name: `User ${i + 1}`,
			rowName,
			date,
			shippingDate,
			vangp,
			deliveryTime,
			plannedStart,
			plannedEnd,
			plannedDuration,
			plannedVs,
			actualStart,
			actualEnd,
			actualDuration,
			actualVs,
			progress,
			progressRate,
			working,
			completed,
			alerts,
		}
	})
}

export const handlers = [
	// Comment out the old tasks.json mock since we're now using the VMA API
	// http.get('/api/tasks.json', async () => {
	// 	const data = makeTasks(40)
	// 	return HttpResponse.json(data)
	// }),

	// Mock for the new VMA API tasks endpoint (fallback for development)
	http.get('/api/v1/tasks', async () => {
		const data = makeTasks(40)
		return HttpResponse.json(data)
	}),

	// Mock for operator-specific tasks endpoint
	http.get('/api/v1/tasks/today/:operatorName', async ({ params }) => {
		const { operatorName } = params

		// Return different task sets based on operator name
		if (operatorName === '南①') {
			return HttpResponse.json(makeTasks(15))
		} else if (operatorName === '南②') {
			return HttpResponse.json(makeTasks(15))
		} else if (operatorName === '北④') {
			return HttpResponse.json(makeTasks(15))
		} else if (operatorName === '北③') {
			return HttpResponse.json(makeTasks(15))
		}

		// Default fallback
		return HttpResponse.json(makeTasks(10))
	}),

	// Mock for operator names endpoint
	http.get('/api/v1/planned-work/operator-names', async () => {
		return HttpResponse.json([
			{ name: '南①' },
			{ name: '南②' },
			{ name: '北④' },
			{ name: '北③' },
		])
	}),

	// Mock for planned work import endpoint
	http.post('/api/v1/planned-work/import', async ({ request }) => {
		// Simulate processing delay
		await new Promise((resolve) => setTimeout(resolve, 2000))

		const formData = await request.formData()
		const file = formData.get('file') as File

		if (!file) {
			return HttpResponse.json(
				{
					totalRecords: 0,
					successfulInserts: 0,
					successfulUpdates: 0,
					failedRecords: 0,
					errors: ['No file provided'],
					warnings: [],
					message: 'No file provided',
				},
				{ status: 400 },
			)
		}

		// Simulate realistic import results
		const totalRecords = Math.floor(Math.random() * 100) + 20
		const successfulInserts = Math.floor(Math.random() * (totalRecords * 0.6)) + 5
		const successfulUpdates = Math.floor(Math.random() * (totalRecords * 0.3)) + 2
		const failedRecords = totalRecords - successfulInserts - successfulUpdates

		// Generate some sample errors and warnings
		const errors =
			failedRecords > 0
				? [
						'Row 15: Missing required field "task_name"',
						'Row 23: Invalid date format in "planned_start"',
						...(failedRecords > 2 ? ['Row 45: Duplicate task ID found'] : []),
					].slice(0, Math.min(failedRecords, 3))
				: []

		const warnings = [
			'Row 8: Task already exists, updated instead of inserted',
			'Row 12: Date format converted from DD/MM/YYYY to YYYY-MM-DD',
			...(Math.random() > 0.5 ? ['Some tasks have overlapping time periods'] : []),
		].slice(0, Math.floor(Math.random() * 3))

		const message =
			failedRecords > 0
				? `Import completed with ${failedRecords} failed records. Please review the errors below.`
				: 'Import completed successfully. All records processed without errors.'

		return HttpResponse.json({
			totalRecords,
			successfulInserts,
			successfulUpdates,
			failedRecords,
			errors,
			warnings,
			message,
		})
	}),
]
