/**
 * Tests for tasks service
 */

import { fetchTasks, fetchTasksForOperator } from '../tasks'
import { mockTasks } from '../../../../__mocks__/mockData'

// Mock the httpClient module
jest.mock('../../../../core/api/httpClient', () => ({
	httpGet: jest.fn(),
}))

// Mock the endpoints module
jest.mock('../../../../core/api/endpoints', () => ({
	endpoints: {
		tasks: '/api/tasks.json',
		tasksToday: '/api/v1/tasks/today',
		tasksTodayForOperator: '/api/v1/tasks/today/{operatorName}',
	},
}))

import { httpGet } from '../../../../core/api/httpClient'
import { endpoints } from '../../../../core/api/endpoints'

const mockHttpGet = httpGet as jest.MockedFunction<typeof httpGet>

describe('Tasks Service', () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe('fetchTasks', () => {
		it('should fetch tasks successfully', async () => {
			// Arrange
			mockHttpGet.mockResolvedValue(mockTasks)

			// Act
			const result = await fetchTasks()

			// Assert
			expect(result).toEqual(mockTasks)
			expect(mockHttpGet).toHaveBeenCalledWith(endpoints.tasksToday, {
				headers: { 'cache-control': 'no-cache' },
			})
			expect(mockHttpGet).toHaveBeenCalledTimes(1)
		})

		it('should fetch tasks with abort signal', async () => {
			// Arrange
			const abortController = new AbortController()
			const signal = abortController.signal
			mockHttpGet.mockResolvedValue(mockTasks)

			// Act
			const result = await fetchTasks(signal)

			// Assert
			expect(result).toEqual(mockTasks)
			expect(mockHttpGet).toHaveBeenCalledWith(endpoints.tasksToday, {
				signal,
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should handle HTTP errors', async () => {
			// Arrange
			const errorMessage = 'HTTP 500'
			mockHttpGet.mockRejectedValue(new Error(errorMessage))

			// Act & Assert
			await expect(fetchTasks()).rejects.toThrow(errorMessage)
			expect(mockHttpGet).toHaveBeenCalledWith(endpoints.tasksToday, {
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should handle network errors', async () => {
			// Arrange
			const networkError = new Error('Network error')
			mockHttpGet.mockRejectedValue(networkError)

			// Act & Assert
			await expect(fetchTasks()).rejects.toThrow('Network error')
		})

		it('should handle empty response', async () => {
			// Arrange
			mockHttpGet.mockResolvedValue([])

			// Act
			const result = await fetchTasks()

			// Assert
			expect(result).toEqual([])
			expect(mockHttpGet).toHaveBeenCalledWith(endpoints.tasksToday, {
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should handle null response', async () => {
			// Arrange
			mockHttpGet.mockResolvedValue(null)

			// Act
			const result = await fetchTasks()

			// Assert
			expect(result).toBeNull()
		})
	})

	describe('fetchTasksForOperator', () => {
		it('should fetch tasks for operator successfully', async () => {
			// Arrange
			mockHttpGet.mockResolvedValue(mockTasks)

			// Act
			const result = await fetchTasksForOperator('TestOperator')

			// Assert
			expect(result).toEqual(mockTasks)
			expect(mockHttpGet).toHaveBeenCalledWith('/api/v1/tasks/today/TestOperator', {
				headers: { 'cache-control': 'no-cache' },
			})
			expect(mockHttpGet).toHaveBeenCalledTimes(1)
		})

		it('should URL encode operator name with special characters', async () => {
			// Arrange
			mockHttpGet.mockResolvedValue(mockTasks)
			const operatorName = '南①'

			// Act
			const result = await fetchTasksForOperator(operatorName)

			// Assert
			expect(result).toEqual(mockTasks)
			expect(mockHttpGet).toHaveBeenCalledWith('/api/v1/tasks/today/%E5%8D%97%E2%91%A0', {
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should fetch tasks for operator with abort signal', async () => {
			// Arrange
			const abortController = new AbortController()
			const signal = abortController.signal
			mockHttpGet.mockResolvedValue(mockTasks)

			// Act
			const result = await fetchTasksForOperator('TestOperator', signal)

			// Assert
			expect(result).toEqual(mockTasks)
			expect(mockHttpGet).toHaveBeenCalledWith('/api/v1/tasks/today/TestOperator', {
				signal,
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should handle HTTP errors for operator tasks', async () => {
			// Arrange
			const errorMessage = 'HTTP 404'
			mockHttpGet.mockRejectedValue(new Error(errorMessage))

			// Act & Assert
			await expect(fetchTasksForOperator('TestOperator')).rejects.toThrow(errorMessage)
			expect(mockHttpGet).toHaveBeenCalledWith('/api/v1/tasks/today/TestOperator', {
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should handle empty response for operator tasks', async () => {
			// Arrange
			mockHttpGet.mockResolvedValue([])

			// Act
			const result = await fetchTasksForOperator('TestOperator')

			// Assert
			expect(result).toEqual([])
			expect(mockHttpGet).toHaveBeenCalledWith('/api/v1/tasks/today/TestOperator', {
				headers: { 'cache-control': 'no-cache' },
			})
		})
	})
})
