import { Canvas, useThree } from '@react-three/fiber'
import { OrbitControls, Text } from '@react-three/drei'
import { useEffect } from 'react'
import * as THREE from 'three'

export interface BarData {
	value: number;
	total: number;
	color?: string;
}

export interface BarGraphProps {
	progress: number;
	total: number;
	width?: number;
	height?: number;
	onChange?: (value: number) => void;
	useOrbitalControls?: boolean;
}

export interface ProgressBarProps {
	progress: number;
	total: number;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({ progress, total }) => {
	const width = progress / total
	const depth = 0.2 // Depth of the bar
	const height = 0.15 // Height of the bar

	return (
		<group>
			{/* Background bar (wireframe) */}
			<lineSegments position={[0, 0, 0]}>
				<edgesGeometry args={[new THREE.BoxGeometry(1, height, depth)]} />
				<lineBasicMaterial color="#cccccc" />
			</lineSegments>

			{/* Progress bar (blue part) */}
			<mesh position={[-0.5 + (width / 2), 0, 0]}>
				<boxGeometry args={[width, height, depth]} />
				<meshStandardMaterial color="#0066cc" />
			</mesh>

			{/* Progress text */}
			<Text
				position={[0, 0, depth / 2 + 0.001]}
				fontSize={0.08}
				color="black"
				anchorX="center"
				anchorY="middle"
			>
				{`${Math.round(progress)}%`}
			</Text>
		</group>
	)
}

// Camera adjuster component
const CameraAdjuster = () => {
	const { camera } = useThree()

	useEffect(() => {
		// Position camera directly in front for straight view
		camera.position.set(0, 0, 1)
		camera.lookAt(0, 0, 0)
		camera.updateProjectionMatrix()
	}, [camera])

	return null
}

export const BarGraph: React.FC<BarGraphProps> = ({
	progress,
	total = 100,
	useOrbitalControls = false,
}) => {
	return (
		<div style={{
			width: '100%',
			height: '100%',
			minHeight: '120px',
			border: '1px solid #eee',
			borderRadius: '4px',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			// Reserve space for future axis labels
			padding: '8px'
		}}>
			<Canvas
				camera={{ position: [0, 0, 1], fov: 50 }}
				style={{
					width: '100%',
					height: '100%',
					maxWidth: '100%',
					maxHeight: '100%'
				}}
			>
				<CameraAdjuster />

				{/* Lighting for subtle 3D effect */}
				<ambientLight intensity={0.8} />
				<directionalLight position={[0, 1, 2]} intensity={0.5} />

				<ProgressBar progress={progress} total={total} />

				{useOrbitalControls && (<OrbitControls
					enableZoom={true}
					enableRotate={true}
					enablePan={true}
				/>)}
			</Canvas>
		</div>
	)
}