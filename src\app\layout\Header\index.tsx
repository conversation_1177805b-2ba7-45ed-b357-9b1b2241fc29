import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ton, Switch, FormControlLabel } from '@mui/material'
import { Link as RouterLink } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useAppStore } from '../../../core/store'

export default function Header() {
	const { t } = useTranslation()
	const autoRefresh = useAppStore(state => state.autoRefresh)
	const setAutoRefresh = useAppStore(s => s.setAutoRefresh)

	return (
		<AppBar position="sticky" sx={{ top: 0 }}>
			<Toolbar variant="dense">
				<Typography variant="h6" sx={{ flexGrow: 1, fontSize: 16 }}>
					VMApp
				</Typography>
				<FormControlLabel
					control={
						<Switch
							checked={autoRefresh}
							onChange={(e) => setAutoRefresh(e.target.checked)}
							color="default"
						/>
					}
					label={t('common.autoRefresh')}
					sx={{ mx: 2, color: 'inherit' }}
				/>
				<Button component={RouterLink} to="/" color="inherit">
					{t('nav.dashboard')}
				</Button>
				<Button component={RouterLink} to="/settings" color="inherit">
					{t('nav.settings')}
				</Button>
			</Toolbar>
		</AppBar>
	)
}
