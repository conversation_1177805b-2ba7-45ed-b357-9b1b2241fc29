import { render, screen } from '@testing-library/react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import TableLegends from '../TableLegends'
import { DEFAULT_TASK_STATE_COLORS } from '../../utils/taskStateColors'

// Mock react-i18next
jest.mock('react-i18next', () => ({
	useTranslation: () => ({
		t: (key: string, options?: { defaultValue?: string }) => options?.defaultValue || key,
	}),
}))

// Mock the store
jest.mock('../../../core/store', () => ({
	useAppStore: jest.fn(() => DEFAULT_TASK_STATE_COLORS),
}))

const theme = createTheme()

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
	<ThemeProvider theme={theme}>{children}</ThemeProvider>
)

describe('TableLegends', () => {
	it('renders legend title and all task states', () => {
		render(
			<TestWrapper>
				<TableLegends />
			</TestWrapper>,
		)

		// Check legend title
		expect(screen.getByText('Legend:')).toBeInTheDocument()

		// Check all task state labels
		expect(screen.getByText('Normal')).toBeInTheDocument()
		expect(screen.getByText('Working')).toBeInTheDocument()
		expect(screen.getByText('Completed')).toBeInTheDocument()
		expect(screen.getByText('Delay')).toBeInTheDocument()
		expect(screen.getByText('Interrupt')).toBeInTheDocument()
	})

	it('renders descriptions in non-compact mode', () => {
		render(
			<TestWrapper>
				<TableLegends compact={false} />
			</TestWrapper>,
		)

		// Check descriptions are visible
		expect(screen.getByText('Not started')).toBeInTheDocument()
		expect(screen.getByText('In progress')).toBeInTheDocument()
		expect(screen.getByText('Finished')).toBeInTheDocument()
		expect(screen.getByText('Behind schedule')).toBeInTheDocument()
		expect(screen.getByText('Interrupted')).toBeInTheDocument()
	})

	it('hides descriptions in compact mode', () => {
		render(
			<TestWrapper>
				<TableLegends compact={true} />
			</TestWrapper>,
		)

		// Check descriptions are not visible
		expect(screen.queryByText('Not started')).not.toBeInTheDocument()
		expect(screen.queryByText('In progress')).not.toBeInTheDocument()
		expect(screen.queryByText('Finished')).not.toBeInTheDocument()
		expect(screen.queryByText('Behind schedule')).not.toBeInTheDocument()
		expect(screen.queryByText('Interrupted')).not.toBeInTheDocument()
	})

	it('uses custom color settings when provided', () => {
		const customColors = {
			normal: '#CUSTOM1',
			working: '#CUSTOM2',
			completed: '#CUSTOM3',
			delay: '#CUSTOM4',
			interrupt: '#CUSTOM5',
		}

		render(
			<TestWrapper>
				<TableLegends colorSettings={customColors} />
			</TestWrapper>,
		)

		// The component should render without errors with custom colors
		expect(screen.getByText('Legend:')).toBeInTheDocument()
		expect(screen.getByText('Normal')).toBeInTheDocument()
	})

	it('applies correct styling for compact mode', () => {
		const { container } = render(
			<TestWrapper>
				<TableLegends compact={true} />
			</TestWrapper>,
		)

		// Check that the component renders (specific styling tests would require more complex setup)
		expect(container.firstChild).toBeInTheDocument()
	})

	it('applies correct styling for normal mode', () => {
		const { container } = render(
			<TestWrapper>
				<TableLegends compact={false} />
			</TestWrapper>,
		)

		// Check that the component renders (specific styling tests would require more complex setup)
		expect(container.firstChild).toBeInTheDocument()
	})
})
