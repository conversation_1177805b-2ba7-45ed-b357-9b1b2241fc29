import { useRef, useState, useLayoutEffect, useEffect } from 'react';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableRow,
	Box,
	IconButton,
	Tooltip,
	TableContainer,
} from '@mui/material'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'
import { getTaskStateColors } from '../../../common/utils/taskStateColors'
import { useAppStore } from '../../../core/store'
import TableLegends from '../../../common/components/TableLegends'
import objectHash from 'object-hash'

function rowsChecksum(rows: Task[]): string {
	const stableList = rows.map(task => ({
		id: task.id,
		date: task.date,
		rowName: task.rowName,
		progressRate: task.progressRate,
		plannedStart: task.plannedStart,
		plannedEnd: task.plannedEnd,
		plannedDuration: task.plannedDuration,
		actualStart: task.actualStart,
		actualEnd: task.actualEnd,
		actualDuration: task.actualDuration,
		working: task.working,
		completed: task.completed
	}));

	return objectHash(stableList, {
		algorithm: 'md5',
		encoding: 'base64',
		unorderedObjects: true, // Important for consistency
		unorderedArrays: false,
		unorderedSets: false
	});
}

function DataTableComponent({
	rows,
	selectedRowId,
	onSelect,
	onFullView,
}: {
	rows: Task[]
	selectedRowId?: number | null
	onSelect?: (row: Task) => void
	onFullView?: (viewType: 'table' | 'chart', title?: string) => void
}) {
	const { t } = useTranslation()
	const taskStateColors = useAppStore((s) => s.taskStateColors)
	const autoRefresh = useAppStore((s) => s.autoRefresh)

	const mainHeaderRef = useRef<HTMLTableRowElement>(null);
	const [mainHeaderHeight, setMainHeaderHeight] = useState(0);

	// Measure main header row height after render
	useLayoutEffect(() => {
		if (mainHeaderRef.current) {
			setMainHeaderHeight(mainHeaderRef.current.getBoundingClientRect().height);
		}
	}, []);

	// Auto-select working tasks when auto-refresh is enabled
	useEffect(() => {
		console.log('🔄 Auto-select useEffect triggered:', {
			autoRefresh,
			hasOnSelect: !!onSelect,
			rowsLength: rows.length,
			selectedRowId,
			workingTasks: rows.filter(r => r.working).map(r => ({ id: r.id, name: r.name, working: r.working }))
		})

		if (!autoRefresh || !onSelect) {
			console.log('❌ Auto-select skipped:', { autoRefresh, hasOnSelect: !!onSelect })
			return
		}

		// Find the first task with working: true
		const workingTask = rows.find(task => task.working === true)
		console.log('🔍 Working task search result:', workingTask ? `${workingTask.name} (ID: ${workingTask.id}, working: ${workingTask.working})` : 'none found')

		if (workingTask) {
			// Only select if it's not already selected to avoid unnecessary re-renders
			console.log('✅ Auto-selecting working task:', workingTask.name, 'Row:', workingTask.rowName)
			onSelect(workingTask)
		} else {
			console.log('🚫 No working tasks found for auto-selection')
		}
	}, [rows, autoRefresh, onSelect, selectedRowId])

	// Constants for sticky header positioning
	const maniHeaderFontSize = '0.75rem'
	const mainHeaderTop = 0
	const mainHeaderZIndex = 5

	return (
		// <Box sx={{ maxHeight: 'calc(50dvh)', display: 'flex', flexDirection: 'column', overflow: 'auto' }}>
		<div className="flex flex-col gap-1.5 h-full">
			<Box className="h-[1rem] flex items-center justify-end px-2">
				{onFullView ? (
					<Tooltip title={t('common.fullscreen') ?? 'Fullscreen'}>
						<IconButton
							size="small"
							aria-label="fullscreen table"
							onClick={() => onFullView('table', 'Table')}
						>
							<FullscreenIcon fontSize="small" />
						</IconButton>
					</Tooltip>
				) : null}
			</Box>
			{/* Table Legends */}
			{!onFullView ? <Box sx={{ px: 2, pb: 1 }}> <TableLegends compact /> </Box> : null}
			<TableContainer component={Box} sx={{
				height: '22rem',
				overflow: 'auto',
				padding: 0,
			}}>
				<Table stickyHeader size="small"
					sx={{
						tableLayout: "auto", // 👈 allow columns to expand to fit content
						"& .MuiTableCell-root": {
							whiteSpace: "nowrap", // 👈 keep text in one line
							userSelect: 'none',
						},
					}}
				>
					<TableHead sx={{
						"& .MuiTableCell-root": {
							cursor: 'default',
						}
					}}>
						{/* Top-level headers */}
						<TableRow ref={mainHeaderRef}>
							{[
								{ label: rows[0]?.name, colSpan: 5, backgroundColor: 'background.paper' },
								{ label: t('table.planned'), colSpan: 3, backgroundColor: '#DAE9F8' },
								{ label: t('table.actual'), colSpan: 3, backgroundColor: '#C1F0C8' }
							].map((header, idx) => (
								<TableCell
									colSpan={header.colSpan}
									key={idx}
									align="center"
									sx={{
										position: 'sticky',
										top: mainHeaderTop,
										zIndex: mainHeaderZIndex,
										backgroundColor: header.backgroundColor,
										borderBottom: '1px solid',
										borderColor: 'divider',
										fontWeight: 600,
										fontSize: maniHeaderFontSize,
									}}
								>
									{header.label}
								</TableCell>
							))}
						</TableRow>
						{/* Sub-headers */}
						<TableRow>
							{[
								t("table.no"),
								t("table.date"),
								t("table.shippingDate"),
								t("table.vangp"),
								t("table.deliveryTime"),
								t("table.start"),
								t("table.end"),
								t("table.duration"),
								t("table.start"),
								t("table.end"),
								t("table.duration"),
							].map((label, idx) => (
								<TableCell
									key={idx}
									align="center"
									sx={{
										position: "sticky",
										top: mainHeaderHeight, // 👈 auto-calculated instead of hardcoded px
										zIndex: 2,
										backgroundColor: "background.paper",
										borderBottom: "1px solid",
										borderColor: "divider",
										fontWeight: 500,
										fontSize: maniHeaderFontSize,
									}}
								>
									{label}
								</TableCell>
							))}
						</TableRow>
					</TableHead>
					<TableBody>
						{rows.map((row, index) => {
							const colors = getTaskStateColors(row, taskStateColors)
							const isSelected = row.id === selectedRowId
							if (isSelected) {
								console.log(`🪪 (DataTable) Row ${row.id} row:${row.rowName} isSelected: ${isSelected}`)
							}
							return (
								<TableRow
									key={`${row.id}-${index}`}
									hover
									selected={isSelected}
									onClick={() => { if (!autoRefresh) { onSelect?.(row) } }}
									sx={{
										cursor: onSelect ? 'pointer' : 'default',
										backgroundColor: `${colors.row} !important`,
										'& .MuiTableCell-root': {
											backgroundColor: 'transparent',
										},
										'&:hover': {
											backgroundColor: `${colors.row} !important`,
											filter: 'brightness(0.95)',
										},
										'&.Mui-selected': {
											backgroundColor: `${colors.header} !important`,
											'& .MuiTableCell-root': {
												color: '#fff',
											},
										},
									}}
									aria-selected={isSelected}
								>
									{[
										row.rowName,
										row.date,
										row.shippingDate,
										row.vangp,
										row.deliveryTime,
										row.plannedStart,
										row.plannedEnd,
										row.plannedDuration,
										row.actualStart,
										row.actualEnd,
										row.actualDuration
									].map((data, idx) => (
										<TableCell key={idx} align="center" sx={{ fontSize: maniHeaderFontSize, }}>{data}</TableCell>
									))}
								</TableRow>
							)
						})}
					</TableBody>
				</Table>
			</TableContainer>
		</div>
		// </Box>
	)
}

const DataTable = memo(
	DataTableComponent,
	(prev, next) => {
		const prevChecksum = rowsChecksum(prev.rows)
		const nextChecksum = rowsChecksum(next.rows)
		const selectedRowSame = prev.selectedRowId === next.selectedRowId
		const rowsSame = prevChecksum === nextChecksum

		console.log('🔍 DataTable memo comparison:', {
			selectedRowSame,
			rowsSame,
			prevSelectedRowId: prev.selectedRowId,
			nextSelectedRowId: next.selectedRowId,
			prevRowsLength: prev.rows.length,
			nextRowsLength: next.rows.length,
			prevWorkingTasks: prev.rows.filter(r => r.working).length,
			nextWorkingTasks: next.rows.filter(r => r.working).length,
			shouldSkipRender: selectedRowSame && rowsSame
		})

		return selectedRowSame && rowsSame
	},
)

export default DataTable
