{"common.fullscreen": "Full screen", "common.cancel": "Cancel", "nav.dashboard": "Dashboard", "nav.settings": "Settings", "loading": "Loading…", "chart.value": "Value", "chart.progress": "Progress", "settings.title": "Settings", "settings.accessibility": "Accessibility", "settings.fontScale": "Font Scale", "settings.colors.title": "Table Row Colors", "settings.colors.normal": "Normal", "settings.colors.working": "Working", "settings.colors.completed": "Completed", "settings.colors.delay": "Delay", "settings.colors.interrupt": "Interrupt", "settings.colors.noteText": "Note: These colors only apply to table rows. Header colors are determined by alert status and remain unchanged.", "settings.language": "Language", "settings.import": "Import", "settings.importPlannedTasks": "Import Planned Tasks", "settings.selectFile": "Select File", "settings.selected": "Selected", "settings.importing": "Importing...", "settings.importSuccess": "Successfully imported {{count}} tasks", "settings.importError": "Failed to import Excel file", "settings.invalidFileType": "Invalid file type. Please upload an Excel file (.xlsx or .xls)", "settings.fileSizeError": "File size too large. Maximum allowed size is 10MB", "settings.languageChanged": "Language changed successfully", "settings.languageError": "Failed to change language", "settings.fontScaleChanged": "Font scale updated", "settings.resetSuccess": "Settings reset to defaults", "settings.resetError": "Failed to reset settings", "settings.reset": "Reset to De<PERSON>ult", "settings.apply": "Apply", "settings.accountManagement": "Account Management", "auth.loginTitle": "<PERSON><PERSON> Required", "auth.username": "Username", "auth.password": "Password", "auth.login": "<PERSON><PERSON>", "auth.loggingIn": "Logging in...", "auth.logout": "Logout", "auth.loginSuccess": "Login successful", "auth.loginFailed": "<PERSON><PERSON> failed", "auth.loginError": "An error occurred during login", "auth.logoutSuccess": "Logged out successfully", "auth.logoutError": "Logout failed", "auth.fillAllFields": "Please fill in all fields", "auth.welcomeUser": "Welcome, {{name}}", "auth.signupTitle": "Create New Account", "auth.signup": "Sign Up", "auth.signingUp": "Creating account...", "auth.signupSuccess": "Account created successfully", "auth.signupFailed": "Signup failed", "auth.signupError": "An error occurred during signup", "auth.createAccount": "Create New Account", "auth.fullName": "Full Name", "auth.confirmPassword": "Confirm Password", "auth.passwordResetTitle": "Reset Password", "auth.resetPassword": "Reset Password", "auth.resettingPassword": "Resetting password...", "auth.passwordResetSuccess": "Password reset successfully", "auth.passwordResetFailed": "Password reset failed", "auth.passwordResetError": "An error occurred during password reset", "auth.newPassword": "New Password", "table.name": "Name", "table.planned": "Planned", "table.actual": "Actual", "table.no": "", "table.date": "Date", "table.shippingDate": "Shipping Date", "table.vangp": "<PERSON><PERSON><PERSON>", "table.deliveryTime": "Delivery Time", "table.start": "Start", "table.end": "End", "table.duration": "Duration", "table.plannedVs": "VS", "table.actualVs": "VS", "table.progress": "Progress", "table.type": "Type", "legends.title": "Legend:", "legends.normal": "Normal", "legends.normalDesc": "Not started", "legends.working": "Working", "legends.workingDesc": "In progress", "legends.completed": "Completed", "legends.completedDesc": "Finished", "legends.delay": "Delay", "legends.delayDesc": "Behind schedule", "legends.interrupt": "Interrupt", "legends.interruptDesc": "Interrupted"}