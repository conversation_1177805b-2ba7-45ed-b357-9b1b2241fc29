/**
 * Tests for Loader component
 */

import { render, screen } from '../../../test-utils'
import Loader from '../Loader'

describe('Loader Component', () => {
	it('should render loading text', () => {
		// Arrange & Act
		render(<Loader />)

		// Assert
		expect(screen.getByText('Loading…')).toBeInTheDocument()
	})

	it('should have proper styling classes', () => {
		// Arrange & Act
		render(<Loader />)

		// Assert
		const loaderElement = screen.getByText('Loading…')
		expect(loaderElement).toHaveClass('p-4')
	})

	it('should render as a div element', () => {
		// Arrange & Act
		render(<Loader />)

		// Assert
		const loaderElement = screen.getByText('Loading…')
		expect(loaderElement.tagName).toBe('DIV')
	})

	it('should be accessible', () => {
		// Arrange & Act
		render(<Loader />)

		// Assert
		expect(screen.getByText('Loading…')).toBeInTheDocument()
	})
})
