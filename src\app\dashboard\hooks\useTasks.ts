import { useEffect, useRef, useCallback, useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { useAppStore } from '../../../core/store'
import { fetchTasksForOperator } from '../services/tasks'
import { isAbortError } from '../../dashboard/components/FullscreenView'
import { getDataRefreshRate } from '../../../core/config/env'
import type { Task } from '../../../models/Task'

interface UseTasksOptions {
	operatorName: string
	enabled?: boolean
}

interface UseTasksReturn {
	tasks: Task[]
	isLoading: boolean
	error: Error | null
	refetch: () => Promise<void>
}

export function useTasks({ operatorName, enabled = true }: UseTasksOptions): UseTasksReturn {
	const { tasksByOperator, setTasksForOperator, getTasksForOperator, autoRefresh } = useAppStore()

	const { ref: inViewRef, inView } = useInView({
		threshold: 0,
		rootMargin: '50px',
	})

	const abortControllerRef = useRef<AbortController | null>(null)
	const intervalRef = useRef<NodeJS.Timeout | null>(null)
	const [isLoading, setIsLoading] = useState(false)
	const [error, setError] = useState<Error | null>(null)

	const tasks = tasksByOperator[operatorName] || []

	const fetchTasks = useCallback(
		async (signal?: AbortSignal) => {
			if (!enabled || isLoading) return

			setIsLoading(true)
			setError(null)

			try {
				const data = await fetchTasksForOperator(operatorName, signal)

				if (signal?.aborted) return

				setTasksForOperator(operatorName, data)
			} catch (error) {
				if (!isAbortError(error) && !signal?.aborted) {
					setError(error as Error)
				}
			} finally {
				setIsLoading(false)
			}
		},
		[operatorName, enabled, isLoading, setTasksForOperator],
	)

	const refetch = useCallback(async () => {
		// Cancel any ongoing request
		if (abortControllerRef.current) {
			abortControllerRef.current.abort()
		}

		// Create new abort controller
		abortControllerRef.current = new AbortController()
		await fetchTasks(abortControllerRef.current.signal)
	}, [fetchTasks])

	// Initial fetch and setup auto-refresh
	useEffect(() => {
		if (!enabled) return

		// Initial fetch
		refetch()

		// Setup auto-refresh interval
		if (autoRefresh && inView) {
			const refreshRate = getDataRefreshRate()

			intervalRef.current = setInterval(() => {
				if (inView) {
					refetch()
				}
			}, refreshRate)
		}

		return () => {
			// Cleanup
			if (abortControllerRef.current) {
				abortControllerRef.current.abort()
			}
			if (intervalRef.current) {
				clearInterval(intervalRef.current)
				intervalRef.current = null
			}
		}
	}, [enabled, autoRefresh, inView, operatorName, refetch])

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort()
			}
			if (intervalRef.current) {
				clearInterval(intervalRef.current)
			}
		}
	}, [])

	return {
		tasks,
		isLoading,
		error,
		refetch,
	}
}
