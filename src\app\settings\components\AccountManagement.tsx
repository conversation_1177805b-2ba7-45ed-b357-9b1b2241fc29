import { Box, Paper, Typography, Button } from '@mui/material'
import React from 'react'
import { useTranslation } from 'react-i18next'

interface AccountManagementProps {
	showSignupModal: () => void
	showPasswordResetModal: () => void
}

export const AccountManagement: React.FC<AccountManagementProps> = (({ showSignupModal, showPasswordResetModal }) => {
	const { t } = useTranslation()

	return (
		<Box sx={{ p: 1, mb: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
			<Typography variant="h6">
				{t('settings.accountManagement', { defaultValue: 'Account Management' })}
			</Typography>
			<Paper sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', gap: 1 }}>
				<Button
					variant="contained"
					component="span"
					onClick={showSignupModal}
				>
					{t('auth.createAccount', { defaultValue: 'Create New Account' })}
				</Button>
				<Button
					variant="outlined"
					component="span"
					onClick={showPasswordResetModal}
				>
					{t('auth.resetPassword', { defaultValue: 'Reset Password' })}
				</Button>
			</Paper>
		</Box>
	)
});


