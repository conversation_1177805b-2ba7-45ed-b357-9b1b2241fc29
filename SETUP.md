# Setup

## Prerequisites

- Node.js 18+ (LTS recommended)
- npm 10+
- Git

## Install

```
npm install
```

## Development

```
npm run dev
```

- MSW mock server starts automatically in development (see `src/mocks/`).
- Open http://localhost:5173
- Hot module replacement enabled for fast development
- Proxy configured for `/api/v1` calls to `http://localhost:8080`

## Build & Preview

```
npm run build
npm run preview
```

- Build process includes locale file copying to `dist/locales/`
- TypeScript compilation with strict mode enabled

## Testing

```
npm run test              # Run all tests
npm run test:watch        # Run tests in watch mode
npm run test:coverage     # Run tests with coverage report
```

- Tests run in jsdom environment
- Coverage reports generated in HTML and LCOV formats
- Test files located in `__tests__` directories and `*.test.tsx` files

## Linting & Formatting

```
npm run lint
npm run typecheck
npm run format
```

- ESLint with React Hooks and TypeScript rules
- Prettier with custom rules (tabs, single quotes, 100 char line width)
- TypeScript strict mode compilation

## Versioning & Releases

```
npm run changeset         # Create a new changeset
npm run version-packages  # Version packages based on changesets
npm run release           # Publish packages to registry
```

- Changeset-based versioning workflow
- Automated changelog generation

## Development Features

- **MSW (Mock Service Worker)**: Browser-side API mocking for development
- **Proxy Configuration**: Automatic forwarding of `/api/v1` calls to backend
- **Locale Management**: Automatic copying of i18n files during build
- **TypeScript**: Strict mode with comprehensive type checking
- **Testing**: Jest + React Testing Library with coverage reporting

## Windows Notes

- PowerShell commands in package scripts are platform-agnostic.
- Git Bash is available if you need POSIX shell compatibility.
- All development tools work natively on Windows.

## macOS Notes

- Use Homebrew to install Node if needed: `brew install node`
- All development tools work natively on macOS.

## Environment Variables

- See `src/core/config/env.ts`. Add variables as `VITE_*` in `.env`.
- Environment variables are processed at build time by Vite.

## Troubleshooting

- **MSW Issues**: Check that `public/mockServiceWorker.js` exists and is up-to-date
- **Type Errors**: Run `npm run typecheck` to identify TypeScript issues
- **Build Failures**: Ensure all dependencies are installed with `npm install`
- **Test Failures**: Check Jest configuration and ensure test environment is properly set up
