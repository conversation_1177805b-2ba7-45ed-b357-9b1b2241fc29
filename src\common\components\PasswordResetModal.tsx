import {
	<PERSON><PERSON>,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	Button,
	Alert,
	Box,
	CircularProgress,
} from '@mui/material'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { resetPassword, type PasswordResetRequest } from '../../core/api/authService'
import {
	validateUsername,
	validatePassword,
	validatePasswordConfirmation,
} from '../../core/config/validationConstants'

interface PasswordResetModalProps {
	open: boolean
	onClose: () => void
	onSuccess: (message: string) => void
}

interface PasswordResetFormData extends PasswordResetRequest {
	confirmPassword: string
}

export default function PasswordResetModal({
	open,
	onClose,
	onSuccess,
}: PasswordResetModalProps) {
	const { t } = useTranslation()
	const [formData, setFormData] = useState<PasswordResetFormData>({
		username: '',
		newPassword: '',
		confirmPassword: '',
	})
	const [isLoading, setIsLoading] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [fieldErrors, setFieldErrors] = useState<{
		username?: string
		newPassword?: string
		confirmPassword?: string
	}>({})

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault()

		// Validate all fields
		const errors: typeof fieldErrors = {}
		const usernameError = validateUsername(formData.username)
		const passwordError = validatePassword(formData.newPassword)
		const confirmPasswordError = validatePasswordConfirmation(
			formData.newPassword,
			formData.confirmPassword,
		)

		if (usernameError) errors.username = usernameError
		if (passwordError) errors.newPassword = passwordError
		if (confirmPasswordError) errors.confirmPassword = confirmPasswordError

		if (Object.keys(errors).length > 0) {
			setFieldErrors(errors)
			return
		}

		setIsLoading(true)
		setError(null)
		setFieldErrors({})

		try {
			const response = await resetPassword({
				username: formData.username,
				newPassword: formData.newPassword,
			})

			if (response.success) {
				onSuccess(
					response.message ||
						t('auth.passwordResetSuccess', {
							defaultValue: 'Password reset successfully',
						}),
				)
				handleClose()
			} else {
				setError(
					response.message ||
						t('auth.passwordResetFailed', { defaultValue: 'Password reset failed' }),
				)
			}
		} catch (error) {
			console.error('Password reset error:', error)
			setError(
				t('auth.passwordResetError', {
					defaultValue: 'An error occurred during password reset',
				}),
			)
		} finally {
			setIsLoading(false)
		}
	}

	const handleClose = () => {
		setFormData({ username: '', newPassword: '', confirmPassword: '' })
		setError(null)
		setFieldErrors({})
		setIsLoading(false)
		onClose()
	}

	const handleInputChange =
		(field: keyof PasswordResetFormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
			setFormData((prev) => ({ ...prev, [field]: e.target.value }))
			// Clear field error when user starts typing
			if (fieldErrors[field]) {
				setFieldErrors((prev) => ({ ...prev, [field]: undefined }))
			}
			if (error) setError(null)
		}

	return (
		<Dialog
			open={open}
			onClose={handleClose}
			maxWidth="sm"
			fullWidth
			PaperProps={{
				component: 'form',
				onSubmit: handleSubmit,
			}}
		>
			<DialogTitle>
				{t('auth.passwordResetTitle', { defaultValue: 'Reset Password' })}
			</DialogTitle>

			<DialogContent>
				<Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
					{error && (
						<Alert severity="error" onClose={() => setError(null)}>
							{error}
						</Alert>
					)}

					<TextField
						label={t('auth.username', { defaultValue: 'Username' })}
						value={formData.username}
						onChange={handleInputChange('username')}
						disabled={isLoading}
						required
						fullWidth
						autoFocus
						error={!!fieldErrors.username}
						helperText={fieldErrors.username}
					/>

					<TextField
						label={t('auth.newPassword', { defaultValue: 'New Password' })}
						type="password"
						value={formData.newPassword}
						onChange={handleInputChange('newPassword')}
						disabled={isLoading}
						required
						fullWidth
						error={!!fieldErrors.newPassword}
						helperText={fieldErrors.newPassword}
					/>

					<TextField
						label={t('auth.confirmPassword', { defaultValue: 'Confirm Password' })}
						type="password"
						value={formData.confirmPassword}
						onChange={handleInputChange('confirmPassword')}
						disabled={isLoading}
						required
						fullWidth
						error={!!fieldErrors.confirmPassword}
						helperText={fieldErrors.confirmPassword}
					/>
				</Box>
			</DialogContent>

			<DialogActions>
				<Button onClick={handleClose} disabled={isLoading}>
					{t('common.cancel', { defaultValue: 'Cancel' })}
				</Button>
				<Button
					type="submit"
					variant="contained"
					disabled={
						isLoading ||
						!formData.username.trim() ||
						!formData.newPassword.trim() ||
						!formData.confirmPassword.trim()
					}
					startIcon={isLoading ? <CircularProgress size={16} /> : null}
				>
					{isLoading
						? t('auth.resettingPassword', { defaultValue: 'Resetting password...' })
						: t('auth.resetPassword', { defaultValue: 'Reset Password' })}
				</Button>
			</DialogActions>
		</Dialog>
	)
}

