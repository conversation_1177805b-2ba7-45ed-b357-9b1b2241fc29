import {
	<PERSON>alog,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	Button,
	Alert,
	Box,
	CircularProgress,
} from '@mui/material'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { signup, type SignupRequest } from '../../core/api/authService'
import {
	validateUsername,
	validatePassword,
	validatePasswordConfirmation,
	validateFullName,
} from '../../core/config/validationConstants'

interface SignupModalProps {
	open: boolean
	onClose: () => void
	onSuccess: (message: string) => void
}

interface SignupFormData extends SignupRequest {
	confirmPassword: string
}

export default function SignupModal({ open, onClose, onSuccess }: SignupModalProps) {
	const { t } = useTranslation()
	const [formData, setFormData] = useState<SignupFormData>({
		username: '',
		password: '',
		confirmPassword: '',
		fullName: '',
	})
	const [isLoading, setIsLoading] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [fieldErrors, setFieldErrors] = useState<{
		username?: string
		password?: string
		confirmPassword?: string
		fullName?: string
	}>({})

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault()

		// Validate all fields
		const errors: typeof fieldErrors = {}
		const usernameError = validateUsername(formData.username)
		const passwordError = validatePassword(formData.password)
		const confirmPasswordError = validatePasswordConfirmation(
			formData.password,
			formData.confirmPassword,
		)
		const fullNameError = validateFullName(formData.fullName)

		if (usernameError) errors.username = usernameError
		if (passwordError) errors.password = passwordError
		if (confirmPasswordError) errors.confirmPassword = confirmPasswordError
		if (fullNameError) errors.fullName = fullNameError

		if (Object.keys(errors).length > 0) {
			setFieldErrors(errors)
			return
		}

		setIsLoading(true)
		setError(null)
		setFieldErrors({})

		try {
			const response = await signup({
				username: formData.username,
				password: formData.password,
				fullName: formData.fullName,
			})

			if (response.success) {
				onSuccess(
					response.message ||
					t('auth.signupSuccess', { defaultValue: 'Account created successfully' }),
				)
				handleClose()
			} else {
				setError(
					response.message || t('auth.signupFailed', { defaultValue: 'Signup failed' }),
				)
			}
		} catch (error) {
			console.error('Signup error:', error)
			setError(t('auth.signupError', { defaultValue: 'An error occurred during signup' }))
		} finally {
			setIsLoading(false)
		}
	}

	const handleClose = () => {
		setFormData({ username: '', password: '', confirmPassword: '', fullName: '' })
		setError(null)
		setFieldErrors({})
		setIsLoading(false)
		onClose()
	}

	const handleInputChange =
		(field: keyof SignupFormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
			setFormData((prev) => ({ ...prev, [field]: e.target.value }))
			// Clear field error when user starts typing
			if (fieldErrors[field]) {
				setFieldErrors((prev) => ({ ...prev, [field]: undefined }))
			}
			if (error) setError(null)
		}

	return (
		<Dialog
			open={open}
			onClose={handleClose}
			maxWidth="sm"
			fullWidth
			slotProps={{
				paper: {
					component: 'form',
					onSubmit: handleSubmit,
				}
			}}
		>
			<DialogTitle>{t('auth.signupTitle', { defaultValue: 'Create New Account' })}</DialogTitle>

			<DialogContent>
				<Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
					{error && (
						<Alert severity="error" onClose={() => setError(null)}>
							{error}
						</Alert>
					)}

					<TextField
						label={t('auth.username', { defaultValue: 'Username' })}
						value={formData.username}
						onChange={handleInputChange('username')}
						disabled={isLoading}
						required
						fullWidth
						autoFocus
						error={!!fieldErrors.username}
						helperText={fieldErrors.username}
					/>

					<TextField
						label={t('auth.password', { defaultValue: 'Password' })}
						type="password"
						value={formData.password}
						onChange={handleInputChange('password')}
						disabled={isLoading}
						required
						fullWidth
						error={!!fieldErrors.password}
						helperText={fieldErrors.password}
					/>

					<TextField
						label={t('auth.confirmPassword', { defaultValue: 'Confirm Password' })}
						type="password"
						value={formData.confirmPassword}
						onChange={handleInputChange('confirmPassword')}
						disabled={isLoading}
						required
						fullWidth
						error={!!fieldErrors.confirmPassword}
						helperText={fieldErrors.confirmPassword}
					/>
				</Box>
			</DialogContent>

			<DialogActions>
				<Button color='error' variant='outlined' onClick={handleClose} disabled={isLoading} sx={{ '&:hover': { borderColor: 'red' } }}>
					{t('common.cancel', { defaultValue: 'Cancel' })}
				</Button>
				<Button
					type="submit"
					variant="contained"
					color='primary'
					disabled={
						isLoading ||
						!formData.username.trim() ||
						!formData.password.trim() ||
						!formData.confirmPassword.trim()
					}
					startIcon={isLoading ? <CircularProgress size={'1rem'} /> : null}
				>
					{isLoading
						? t('auth.signingUp', { defaultValue: 'Creating account...' })
						: t('auth.signup', { defaultValue: 'Sign Up' })}
				</Button>
			</DialogActions>
		</Dialog>
	)
}

