import {
	<PERSON>alog,
	<PERSON>alogTitle,
	DialogContent,
	DialogActions,
	TextField,
	Button,
	Alert,
	Box,
	CircularProgress,
} from '@mui/material'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { login, type LoginRequest } from '../../core/api/authService'

interface LoginModalProps {
	open: boolean
	onClose: () => void
	onSuccess: (username: string, fullName?: string) => void
}

export default function LoginModal({ open, onClose, onSuccess }: LoginModalProps) {
	const { t } = useTranslation()
	const navigate = useNavigate()
	const [credentials, setCredentials] = useState<LoginRequest>({ username: '', password: '' })
	const [isLoading, setIsLoading] = useState(false)
	const [error, setError] = useState<string | null>(null)

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault()

		if (!credentials.username.trim() || !credentials.password.trim()) {
			setError(t('auth.fillAllFields', { defaultValue: 'Please fill in all fields' }))
			return
		}

		setIsLoading(true)
		setError(null)

		try {
			const response = await login(credentials)

			if (response.success) {
				onSuccess(response.username || credentials.username, response.fullName)
				handleClose()
			} else {
				setError(response.message || t('auth.loginFailed', { defaultValue: 'Login failed' }))
			}
		} catch (error) {
			console.error('Login error:', error)
			setError(t('auth.loginError', { defaultValue: 'An error occurred during login' }))
		} finally {
			setIsLoading(false)
		}
	}

	const handleClose = () => {
		setCredentials({ username: '', password: '' })
		setError(null)
		setIsLoading(false)
		onClose()
	}

	const handleCancel = () => {
		handleClose()
		navigate('/')
	}

	const handleInputChange = (field: keyof LoginRequest) => (e: React.ChangeEvent<HTMLInputElement>) => {
		setCredentials(prev => ({ ...prev, [field]: e.target.value }))
		if (error) setError(null) // Clear error when user starts typing
	}

	return (
		<Dialog
			open={open}
			onClose={handleClose}
			maxWidth="sm"
			fullWidth
			PaperProps={{
				component: 'form',
				onSubmit: handleSubmit,
			}}
		>
			<DialogTitle>
				{t('auth.loginTitle', { defaultValue: 'Login Required' })}
			</DialogTitle>

			<DialogContent>
				<Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
					{error && (
						<Alert severity="error" onClose={() => setError(null)}>
							{error}
						</Alert>
					)}

					<TextField
						label={t('auth.username', { defaultValue: 'Username' })}
						value={credentials.username}
						onChange={handleInputChange('username')}
						disabled={isLoading}
						required
						fullWidth
						autoFocus
					/>

					<TextField
						label={t('auth.password', { defaultValue: 'Password' })}
						type="password"
						value={credentials.password}
						onChange={handleInputChange('password')}
						disabled={isLoading}
						required
						fullWidth
					/>
				</Box>
			</DialogContent>

			<DialogActions>
				<Button
					color='error'
					variant="outlined"
					onClick={handleCancel}
					disabled={isLoading}
				>
					{t('common.cancel', { defaultValue: 'Cancel' })}
				</Button>
				<Button
					color='primary'
					type="submit"
					variant="contained"
					disabled={isLoading || !credentials.username.trim() || !credentials.password.trim()}
					startIcon={isLoading ? <CircularProgress size={16} /> : null}
				>
					{isLoading
						? t('auth.loggingIn', { defaultValue: 'Logging in...' })
						: t('auth.login', { defaultValue: 'Login' })
					}
				</Button>
			</DialogActions>
		</Dialog>
	)
}
