import { getApiBaseUrl, getDebugHttp } from '../config/env'
import { getToken } from '../utils/tokenStorage'

async function buildUrl(url: string): Promise<string> {
	// If URL is already absolute, return as-is
	if (url.startsWith('http://') || url.startsWith('https://')) {
		return url
	}

	const debugHttp = await getDebugHttp()

	// Check if we're in development mode with MSW enabled
	// Use a safer approach that works in both browser and test environments
	let isDev = false
	let useMSW = false

	try {
		if (typeof window !== 'undefined' && typeof globalThis !== 'undefined') {
			// Try to access import.meta safely
			const globalWithImport = globalThis as unknown as {
				import?: { meta?: { env?: Record<string, string | boolean> } }
			}
			const importMeta = globalWithImport.import?.meta
			if (importMeta?.env) {
				isDev = importMeta.env.useMSW = importMeta.env.VITE_USE_MSW === 'true'
			}
		}
	} catch {
		// Fallback: assume not in MSW mode if we can't detect it
		isDev = false
		useMSW = false
	}

	if (isDev && useMSW) {
		// In MSW mode, use relative URLs so MSW can intercept them
		if (debugHttp) {
			console.debug(
				`[HTTP Client] buildUrl: MSW mode - using relative URL="${url}" (will be intercepted by MSW)`,
			)
		}
		return url
	}

	// Get API base URL from configuration
	const apiBaseUrl = await getApiBaseUrl()

	if (debugHttp) {
		console.debug(
			`[HTTP Client] buildUrl: input="${url}", apiBaseUrl="${apiBaseUrl}", useMSW=${useMSW}`,
		)
	}

	if (apiBaseUrl) {
		const fullUrl = `${apiBaseUrl}${url}`
		if (debugHttp) {
			console.debug(`[HTTP Client] buildUrl: using full URL="${fullUrl}"`)
		}
		return fullUrl
	}

	// Otherwise, use relative URL (for development with Vite proxy)
	if (debugHttp) {
		console.debug(
			`[HTTP Client] buildUrl: using relative URL="${url}" (will be proxied by Vite)`,
		)
	}
	return url
}

/**
 * Get authorization headers with JWT token
 */
function getAuthHeaders(): Record<string, string> {
	const token = getToken()
	if (token) {
		return {
			Authorization: `Bearer ${token}`,
		}
	}
	return {}
}

export async function httpGet<T>(
	url: string,
	init?: RequestInit & { signal?: AbortSignal },
): Promise<T> {
	const fullUrl = await buildUrl(url)
	const debugHttp = await getDebugHttp()

	if (debugHttp) {
		console.debug(`[HTTP Client] GET request to: ${fullUrl}`)
	}

	// Merge auth headers with any existing headers
	const authHeaders = getAuthHeaders()
	const headers = { ...authHeaders, ...init?.headers }

	const res = await fetch(fullUrl, { ...init, headers })

	if (debugHttp) {
		console.debug(`[HTTP Client] GET response: ${res.status} ${res.statusText}`)
	}

	if (!res.ok) throw new Error(`HTTP ${res.status}`)
	return (await res.json()) as T
}

export async function httpPost<T>(
	url: string,
	body?: FormData | string | object,
	init?: RequestInit & { signal?: AbortSignal },
): Promise<T> {
	const fullUrl = await buildUrl(url)
	const debugHttp = await getDebugHttp()

	let requestBody: FormData | string | undefined
	const authHeaders = getAuthHeaders()
	const headers: Record<string, string> = { ...authHeaders, ...init?.headers } as Record<
		string,
		string
	>

	if (body instanceof FormData) {
		// Let the browser set Content-Type for FormData (includes boundary)
		requestBody = body
		if (debugHttp) {
			console.debug(
				`[HTTP Client] POST ${fullUrl} with FormData (${body.get('file') ? 'file upload' : 'form data'})`,
			)
		}
	} else if (typeof body === 'string') {
		requestBody = body
		headers['Content-Type'] = 'text/plain'
		if (debugHttp) {
			console.debug(`[HTTP Client] POST ${fullUrl} with text body`)
		}
	} else if (body && typeof body === 'object') {
		requestBody = JSON.stringify(body)
		headers['Content-Type'] = 'application/json'
		if (debugHttp) {
			console.debug(`[HTTP Client] POST ${fullUrl} with JSON body`)
		}
	} else {
		if (debugHttp) {
			console.debug(`[HTTP Client] POST ${fullUrl} with no body`)
		}
	}

	try {
		const res = await fetch(fullUrl, {
			...init,
			method: 'POST',
			headers,
			body: requestBody,
		})

		if (debugHttp) {
			console.debug(`[HTTP Client] Response: ${res.status} ${res.statusText}`)
		}

		if (!res.ok) {
			const errorText = await res.text().catch(() => 'Unknown error')
			console.error(`[HTTP Client] Error ${res.status}: ${errorText}`)
			throw new Error(`HTTP ${res.status}: ${res.statusText}`)
		}

		return (await res.json()) as T
	} catch (error) {
		console.error(`[HTTP Client] Request failed:`, error)
		throw error
	}
}
