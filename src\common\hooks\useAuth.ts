import { useState, useEffect, useCallback } from 'react'
import { getToken, removeToken, getUserFromToken, isTokenExpired } from '../../core/utils/tokenStorage'
import { checkAuthStatus, logout as logoutService } from '../../core/api/authService'

export interface AuthUser {
	username: string
	fullName: string
	userId: number
}

export interface AuthState {
	isAuthenticated: boolean
	isLoading: boolean
	user: AuthUser | null
}

/**
 * Custom hook for managing authentication state
 * Provides reactive authentication state that updates when tokens change
 */
export function useAuth() {
	const [authState, setAuthState] = useState<AuthState>({
		isAuthenticated: false,
		isLoading: true,
		user: null,
	})

	// Check if user is authenticated based on token
	const checkLocalAuth = useCallback((): AuthState => {
		const token = getToken()
		
		if (!token) {
			return {
				isAuthenticated: false,
				isLoading: false,
				user: null,
			}
		}

		// Check if token is expired
		if (isTokenExpired(token)) {
			// Remove expired token
			removeToken()
			return {
				isAuthenticated: false,
				isLoading: false,
				user: null,
			}
		}

		// Get user info from token
		const user = getUserFromToken(token)
		if (!user) {
			// Invalid token format
			removeToken()
			return {
				isAuthenticated: false,
				isLoading: false,
				user: null,
			}
		}

		return {
			isAuthenticated: true,
			isLoading: false,
			user,
		}
	}, [])

	// Verify authentication with server
	const verifyAuthWithServer = useCallback(async (): Promise<AuthState> => {
		try {
			const response = await checkAuthStatus()
			if (response.success && response.username) {
				const user: AuthUser = {
					username: response.username,
					fullName: response.fullName || '',
					userId: 0, // Server doesn't return userId in status check
				}
				return {
					isAuthenticated: true,
					isLoading: false,
					user,
				}
			} else {
				// Server says not authenticated, remove local token
				removeToken()
				return {
					isAuthenticated: false,
					isLoading: false,
					user: null,
				}
			}
		} catch (error) {
			console.warn('Server auth check failed:', error)
			// If server check fails, fall back to local token check
			return checkLocalAuth()
		}
	}, [checkLocalAuth])

	// Initialize authentication state
	useEffect(() => {
		const initAuth = async () => {
			// First check local token
			const localAuth = checkLocalAuth()
			
			if (localAuth.isAuthenticated) {
				// If locally authenticated, verify with server
				const serverAuth = await verifyAuthWithServer()
				setAuthState(serverAuth)
			} else {
				// Not locally authenticated
				setAuthState(localAuth)
			}
		}

		initAuth()
	}, [checkLocalAuth, verifyAuthWithServer])

	// Listen for storage changes (token updates from other tabs/windows)
	useEffect(() => {
		const handleStorageChange = (event: StorageEvent) => {
			if (event.key === 'vma_auth_token') {
				// Token changed in localStorage, update auth state
				const newAuthState = checkLocalAuth()
				setAuthState(newAuthState)
			}
		}

		window.addEventListener('storage', handleStorageChange)
		return () => {
			window.removeEventListener('storage', handleStorageChange)
		}
	}, [checkLocalAuth])

	// Listen for custom token change events (same tab)
	useEffect(() => {
		const handleTokenChange = () => {
			const newAuthState = checkLocalAuth()
			setAuthState(newAuthState)
		}

		window.addEventListener('tokenChanged', handleTokenChange)
		return () => {
			window.removeEventListener('tokenChanged', handleTokenChange)
		}
	}, [checkLocalAuth])

	// Logout function
	const logout = useCallback(async () => {
		try {
			// Call server logout
			await logoutService()
		} catch (error) {
			console.warn('Server logout failed:', error)
		} finally {
			// Always update local state
			setAuthState({
				isAuthenticated: false,
				isLoading: false,
				user: null,
			})
			// Dispatch custom event for same-tab updates
			window.dispatchEvent(new Event('tokenChanged'))
		}
	}, [])

	// Login success handler
	const handleLoginSuccess = useCallback((username: string, fullName?: string) => {
		const user: AuthUser = {
			username,
			fullName: fullName || '',
			userId: 0, // Will be updated when token is parsed
		}
		
		// Update state immediately
		setAuthState({
			isAuthenticated: true,
			isLoading: false,
			user,
		})
		
		// Dispatch custom event for same-tab updates
		window.dispatchEvent(new Event('tokenChanged'))
	}, [])

	return {
		...authState,
		logout,
		handleLoginSuccess,
		refresh: () => {
			const newAuthState = checkLocalAuth()
			setAuthState(newAuthState)
		},
	}
}
