/**
 * Token storage utilities for JWT authentication
 */

const TOKEN_KEY = 'vma_auth_token'

export interface TokenPayload {
	sub: string
	fullName: string
	userId: number
	exp: number
	iat: number
	iss: string
}

/**
 * Store JWT token in localStorage
 */
export function setToken(token: string): void {
	try {
		localStorage.setItem(TOKEN_KEY, token)
		// Dispatch custom event for same-tab token changes
		window.dispatchEvent(new Event('tokenChanged'))
	} catch (error) {
		console.warn('Failed to store token in localStorage:', error)
	}
}

/**
 * Get JWT token from localStorage
 */
export function getToken(): string | null {
	try {
		return localStorage.getItem(TOKEN_KEY)
	} catch (error) {
		console.warn('Failed to retrieve token from localStorage:', error)
		return null
	}
}

/**
 * Remove JWT token from localStorage
 */
export function removeToken(): void {
	try {
		localStorage.removeItem(TOKEN_KEY)
		// Dispatch custom event for same-tab token changes
		window.dispatchEvent(new Event('tokenChanged'))
	} catch (error) {
		console.warn('Failed to remove token from localStorage:', error)
	}
}

/**
 * Check if user has a valid token (basic check - doesn't validate expiration)
 */
export function hasToken(): boolean {
	const token = getToken()
	return token !== null && token.length > 0
}

/**
 * Parse JWT token payload (without verification)
 * Note: This is for client-side display purposes only, server should always verify
 */
export function parseTokenPayload(token: string): TokenPayload | null {
	try {
		const parts = token.split('.')
		if (parts.length !== 3) {
			throw new Error('Invalid token format')
		}

		const payload = parts[1]
		const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
		console.log('decoded:', decoded)
		return JSON.parse(decoded)
	} catch (error) {
		console.warn('Failed to parse token payload:', error)
		return null
	}
}

/**
 * Check if token is expired (client-side check only)
 */
export function isTokenExpired(token: string): boolean {
	try {
		const payload = parseTokenPayload(token)
		if (!payload || !payload.exp) {
			return true
		}

		const currentTime = Math.floor(Date.now() / 1000)
		return payload.exp < currentTime
	} catch (error) {
		console.warn('Failed to check token expiration:', error)
		return true
	}
}

/**
 * Get user info from token
 */
export function getUserFromToken(
	token: string,
): { username: string; fullName: string; userId: number } | null {
	try {
		const payload = parseTokenPayload(token)
		if (!payload) {
			return null
		}

		return {
			username: payload.sub || '',
			fullName: payload.fullName || '',
			userId: payload.userId || 0,
		}
	} catch (error) {
		console.warn('Failed to extract user from token:', error)
		return null
	}
}
