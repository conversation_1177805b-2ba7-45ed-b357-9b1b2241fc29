# Environment Configuration Setup

This document describes the environment configuration setup for the VMA web application.

## Overview

The application uses a flexible configuration approach for API endpoints:

- **Development**: Uses Vite proxy (empty API_BASE_URL) for local development
- **Production**: Uses `public/config.json` for runtime API configuration (can be modified after build)

## Environment Files

### `.env.development`

```env
# Development environment - use empty string to rely on Vite proxy
VITE_API_BASE_URL=
VITE_DEBUG_HTTP=true
# Set to 'true' to enable MSW mocking, leave empty/false to use Vite proxy
VITE_USE_MSW=false
```

### `.env.development.mock`

```env
# Development environment with MSW mocking enabled
VITE_API_BASE_URL=
VITE_DEBUG_HTTP=true
# Enable MSW for API mocking instead of using Vite proxy
VITE_USE_MSW=true
```

### `.env.production`

```env
# Production environment - API configuration loaded from public/config.json
VITE_API_BASE_URL=
VITE_DEBUG_HTTP=false
```

### `public/config.json`

```json
{
	"apiBaseUrl": "http://*************:8089",
	"debugHttp": false
}
```

This file contains the runtime configuration for production builds:

- **apiBaseUrl**: The API server URL (can be modified after build)
- **debugHttp**: Enable/disable HTTP request logging

## Configuration Files

### `src/core/config/env.ts`

```typescript
// Environment configuration - uses Vite environment variables
export const ENV = {
	// API_BASE_URL is set by Vite at build time from VITE_API_BASE_URL
	API_BASE_URL: import.meta.env.VITE_API_BASE_URL || '',
	DEBUG_HTTP: import.meta.env.VITE_DEBUG_HTTP === 'true' || false,
}
```

### `vite.config.ts`

- Configured to load environment variables with `VITE_` prefix
- Development proxy routes `/api/v1` to `http://localhost:8080`
- Production build embeds the API URL directly in the JavaScript bundle

## How It Works

### Development Mode (`npm run dev`)

1. Vite loads `.env.development`
2. `VITE_API_BASE_URL` is empty, so API calls use relative URLs
3. `VITE_USE_MSW` is false, so MSW is disabled
4. Vite proxy intercepts `/api/v1` requests and forwards to `http://localhost:8080`

### Development Mode with Mocking (`npm run dev:mock`)

1. Vite loads `.env.development.mock`
2. `VITE_API_BASE_URL` is empty, so API calls use relative URLs
3. `VITE_USE_MSW` is true, so MSW intercepts all API requests
4. MSW returns mock data instead of forwarding to real API

### Production Build (`npm run build`)

1. Vite loads `.env.production` (VITE_API_BASE_URL is empty)
2. `public/config.json` is copied to `dist/config.json`
3. Application loads API configuration from `/config.json` at runtime
4. API endpoint can be modified after build by editing `dist/config.json`

## Development Scripts

### Using Vite Proxy (Default)

```bash
npm run dev
```

- Uses Vite proxy to forward API calls to `http://localhost:8080`
- MSW is disabled
- Best for testing against real backend API

### Using MSW Mocking

```bash
npm run dev:mock
```

- Enables MSW to intercept and mock API calls
- Returns mock data instead of calling real API
- Best for frontend development without backend dependency

## Post-Build Configuration

After building the application (`npm run build`), you can modify the API endpoint without rebuilding:

### Changing API Endpoint

1. **Edit the configuration file:**

    ```bash
    # Edit dist/config.json
    {
      "apiBaseUrl": "http://your-new-server:8080",
      "debugHttp": false
    }
    ```

2. **Deploy the updated files:**
    - The application will automatically load the new API endpoint on next page load
    - No rebuild or recompilation required

### Benefits

- ✅ **Easy Deployment**: Change API endpoints for different environments
- ✅ **No Rebuild Required**: Modify configuration after build
- ✅ **Environment Flexibility**: Same build can work with different API servers
- ✅ **Quick Updates**: Change endpoints without touching source code

## Verification

Run the verification script to check the configuration:

```bash
npm run verify-env
```

This script will:

- ✅ Check that environment files exist and have correct content
- ✅ Verify that the production API URL is embedded in built files
- ✅ Confirm the setup is working correctly

## Testing

All tests use mocked environment variables to ensure consistent behavior:

- HTTP client tests verify URL building logic
- Environment configuration tests verify proper variable loading
- Component tests work with mocked API responses

## Benefits

1. **Environment-Specific Configuration**: Different URLs for dev/prod
2. **Build-Time Optimization**: API URLs are embedded at build time
3. **Development Convenience**: Proxy handles CORS and routing in development
4. **Production Performance**: Direct API calls without proxy overhead
5. **Easy Deployment**: Change environment files to target different servers
